<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiouser.dao.ITenantDao">
    <resultMap id="TenantsMap" type="com.digiwin.escloud.aiouser.model.tenant.Tenant">
        <result property="id" column="id"/>
        <result property="sid" column="sid"/>
        <result property="name" column="name"/>
        <result property="customerFullNameCH" column="customerFullNameCH"/>
        <result property="customer_id" column="customer_id"/>
        <result property="taxCode" column="taxCode"/>
        <result property="status" column="status"/>
        <result property="contacts" column="contacts"/>
        <result property="registerPhone" column="registerPhone"/>
        <result property="address" column="address"/>
        <result property="email" column="email"/>
        <result property="phone" column="phone"/>
        <result property="isv" column="isv"/>
        <result property="cellphone_prefix" column="cellphone_prefix"/>
        <result property="telephone" column="telephone"/>
        <result property="attention" column="attention"/>
        <result property="installed" column="installed"/>
        <result property="receiveWarningMail" column="receiveWarningMail"/>
        <collection property="tenantContracts" columnPrefix="tc_"
                     resultMap="TenantContractsMap" />
        <collection property="supplierTenantMaps" columnPrefix="st_"
                    resultMap="SupplierTenantMap" />
        <collection property="tenantModuleContracts" columnPrefix="tmc_"
                    resultMap="TenantModuleContractMap" />
    </resultMap>

    <resultMap id="SubscribeTenantMap" type="com.digiwin.escloud.aiouser.model.tenant.SubscribeTenant" extends="TenantsMap">
    </resultMap>

    <resultMap id="TenantsMapV2" type="com.digiwin.escloud.aiouser.model.tenant.NotExpiredTenant">
        <result property="id" column="id"/>
        <result property="sid" column="sid"/>
        <result property="name" column="name"/>
        <result property="customer_id" column="customer_id"/>
        <result property="taxCode" column="taxCode"/>
        <result property="status" column="status"/>
        <result property="contacts" column="contacts"/>
        <result property="registerPhone" column="registerPhone"/>
        <result property="address" column="address"/>
        <result property="email" column="email"/>
        <result property="phone" column="phone"/>
        <result property="isv" column="isv"/>
        <result property="supplierType" column="supplierType"/>
        <result property="supplierSid" column="supplier_sid"/>
        <result property="cellphone_prefix" column="cellphone_prefix"/>
        <result property="telephone" column="telephone"/>
        <result property="attention" column="attention"/>
        <result property="receiveWarningMail" column="receiveWarningMail"/>
        <collection property="tenantContracts" columnPrefix="tc_"
                    resultMap="TenantContractsMap" />
        <collection property="supplierTenantMaps" columnPrefix="st_"
                    resultMap="SupplierTenantMap" />
        <collection property="tenantModuleContracts" columnPrefix="tmc_"
                    resultMap="TenantModuleContractMap" />
    </resultMap>
    <resultMap id="TenantContractsMap" type="com.digiwin.escloud.aiouser.model.tenant.TenantContract">
        <result property="id" column="id"/>
        <result property="sid" column="sid"/>
        <result property="productCode" column="productCode"/>
        <result property="productShortName" column="productShortName"/>
        <result property="serviceType" column="serviceType"/>
        <result property="serviceStaffId" column="serviceStaffId"/>
        <result property="serviceStaffName" column="serviceStaffName"/>
        <result property="contractState" column="contractState"/>
        <result property="contractStartDate" column="contractStartDate"/>
        <result property="contractExpiryDate" column="contractExpiryDate"/>
        <result property="industryId" column="industryId"/>
        <result property="areaId" column="areaId"/>
        <result property="access" column="access"/>
        <result property="updateTime" column="updateTime"/>
    </resultMap>
    <resultMap id="SupplierTenantMap" type="com.digiwin.escloud.aiouser.model.supplier.SupplierTenantMap">
        <result property="id" column="id"/>
        <result property="sid" column="sid"/>
        <result property="eid" column="eid"/>
        <result property="serviceCode" column="serviceCode"/>
    </resultMap>
    <resultMap id="TenantModuleContractMap" type="com.digiwin.escloud.aiouser.model.tenant.TenantModuleContract">
        <result property="id" column="id"/>
        <result property="sid" column="sid"/>
        <result property="eid" column="eid"/>
        <result property="moduleId" column="moduleId"/>
        <result property="moduleCode" column="moduleCode"/>
        <result property="moduleName" column="moduleName"/>
        <result property="serviceIsvSid" column="serviceIsvSid"/>
        <result property="serviceIsvName" column="serviceIsvName"/>
        <result property="status" column="status"/>
        <result property="startDate" column="startDate"/>
        <result property="endDate" column="endDate"/>
        <result property="expired" column="expired"/>
        <result property="expiringSoon" column="expiringSoon"/>
        <result property="daysUntilExpiration" column="daysUntilExpiration"/>
        <result property="marketUrl" column="marketUrl"/>
        <result property="enableRenewal" column="enableRenewal"/>
        <result property="cloudMarketProductId" column="cloudMarketProductId"/>
        <result property="days" column="days"/>
        <result property="serverCount" column="serverCount"/>
        <result property="workStationCount" column="workStationCount"/>
        <result property="iotCount" column="iotCount"/>
        <result property="staffId" column="staffId"/>
        <result property="staffName" column="staffName"/>
        <result property="staffEmail" column="staffEmail"/>
        <result property="openId" column="openId"/>
        <result property="userName" column="userName"/>
        <result property="noticeEmail" column="noticeEmail"/>
        <result property="itemNo" column="itemNo"/>
        <collection property="tenantModuleContractDetailList" columnPrefix="tmcd_"
                    resultMap="TenantModuleContractDetailMap"/>
    </resultMap>
    <resultMap id="TenantModuleContractDetailMap" type="com.digiwin.escloud.aiouser.model.tenant.TenantModuleContractDetail">
        <result property="id" column="id"/>
        <result property="tmcId" column="tmcId"/>
        <result property="samcId" column="samcId"/>
        <result property="availableCount" column="availableCount"/>
        <result property="usedCount" column="usedCount"/>

        <result property="classCode" column="classCode"/>
        <result property="className" column="className"/>
        <result property="className_CN" column="className_CN"/>
        <result property="className_TW" column="className_TW"/>

        <result property="contractIsValid" column="contractIsValid"/>
        <result property="isContainHoldAuth" column="isContainHoldAuth"/>
        <result property="independentAuth" column="independentAuth"/>

        <collection property="supplierAiopsModuleClassDetailList" columnPrefix="samcd_"
                    resultMap="SupplierAiopsModuleClassDetailMap"/>
    </resultMap>
    <resultMap id="SupplierAiopsModuleClassDetailMap" type="com.digiwin.escloud.aiouser.model.supplier.SupplierAiopsModuleClassDetail">
        <id property="id" column="id"/>
        <result property="samcId" column="samcId"/>
        <result property="aiopsItemType" column="aiopsItemType"/>
        <result property="aiopsItem" column="aiopsItem"/>
        <result property="holdAuth" column="holdAuth"/>
        <result property="isAuthByInstance" column="isAuthByInstance"/>
        <result property="execParamsModelCode" column="execParamsModelCode"/>
        <result property="allowDuplicateMapping" column="allowDuplicateMapping"/>
    </resultMap>
    <resultMap id="UsersMap" type="com.digiwin.escloud.aiouser.model.user.User">
    </resultMap>

    <select id="getTenants" resultMap="TenantsMap">
        SELECT a.sid, a.id, a.name, a.customer_id, a.taxCode, a.status, a.contacts,
               b.id AS tc_id, b.eid AS tc_eid, b.sid AS tc_sid, b.productCode AS tc_productCode,
               sp.productCategory AS tc_productShortName, b.serviceType AS tc_serviceType,
               b.serviceStaffId AS tc_serviceStaffId, b.serviceStaffName AS tc_serviceStaffName,
               b.contractState AS tc_contractState, b.contractStartDate AS tc_contractStartDate,
               b.contractExpiryDate AS tc_contractExpiryDate, b.industryId AS tc_industryId, b.areaId AS tc_areaId,
               b.access AS tc_access,
               c.id AS st_id, c.sid AS st_sid, c.eid AS st_eid, c.serviceCode AS st_serviceCode,
               d.attention, d.receiveWarningMail
        FROM tenant a
        LEFT JOIN tenant_contract b ON a.sid=b.eid
        LEFT JOIN supplier_product sp ON sp.sid=b.sid AND sp.productCode=b.productCode
        LEFT JOIN supplier_tenant_map c ON a.sid=c.eid
        LEFT JOIN user_tenant_attention d ON d.eid=a.sid AND d.userSid=#{userSid}
        WHERE 1=1 AND c.sid=#{sid}
        <if test="id != '' and name != ''">
            AND (a.id LIKE "%"#{id}"%" OR a.name LIKE "%"#{name}"%")
        </if>
        <if test="eid != 0">
            AND a.sid=#{eid}
        </if>
        <if test="serviceType != null">
            <if test="serviceType == 0">
                AND b.hasOwnerService=true
            </if>
            <if test="serviceType == 1">
                AND b.hasTextService=true
            </if>
        </if>
        <if test="status != null">
            AND a.status=#{status}
        </if>
        <if test="contractExpiryDate != null">
            AND b.contractExpiryDate<![CDATA[<=]]>#{contractExpiryDate}
        </if>
        <if test="tenantSids != null and tenantSids.size()>0">
            AND a.sid IN
            <foreach collection="tenantSids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="role!='superManager'">
            AND EXISTS(select eid FROM tenant_contract tc WHERE tc.sid = c.sid AND tc.productCode IN
            <foreach collection="productCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND tc.eid = c.eid)
        </if>
        <if test="attention == true">
            AND EXISTS(SELECT 1 FROM user_tenant_attention uta WHERE uta.sid = #{sid} AND uta.attention=#{attention}
            AND uta.eid = a.sid AND uta.userSid = #{userSid})
        </if>
        <if test="attention == false">
            AND NOT EXISTS(SELECT 1 FROM user_tenant_attention uta
            WHERE uta.userSid=#{userSid} AND uta.sid=#{sid} AND uta.attention=1 AND a.sid=uta.eid
            )
        </if>
        ORDER BY a.id ASC
    </select>

    <select id="getTenantList" resultType="com.digiwin.escloud.aiouser.model.tenant.TenantInfo">
        SELECT c.sid,a.sid eid,a.NAME name,c.serviceCode
        FROM supplier_tenant_map c
        LEFT JOIN tenant a ON c.eid = a.sid
        where c.sid=#{sid}
    </select>
    <select id="getTenantSids" resultType="java.lang.Long">
        select distinct a.sid
        from tenant a
        left join tenant_contract b on a.sid=b.eid
        left join supplier_tenant_map c on a.sid=c.eid
        where 1=1 and c.sid=#{sid}
        <if test="id != '' and name != ''">
            AND (a.id like "%"#{id}"%" or a.name like "%"#{name}"%")
        </if>
        <if test="eid != 0">
            AND a.sid = #{eid}
        </if>
        <if test="serviceType != null">
            AND b.serviceType=#{serviceType}
        </if>
        <if test="status != null">
            AND a.status=#{status}
        </if>
        <if test="contractExpiryDate != null">
            AND b.contractExpiryDate <![CDATA[<=]]>#{contractExpiryDate}
        </if>
        <if test="tenantSids != null and tenantSids.size()>0">
            and a.sid IN
            <foreach collection="tenantSids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="role!='superManager'">
            and exists(select eid from tenant_contract tc where tc.sid = c.sid and tc.productCode IN
            <foreach collection="productCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and tc.eid = c.eid)
        </if>
        <if test="attention == true">
            and exists(select 1 from user_tenant_attention uta where uta.sid = #{sid} AND uta.attention=#{attention}
            and uta.eid = a.sid and uta.userSid = #{userSid})
        </if>
        <if test="attention == false">
            and not exists(select 1 from user_tenant_attention uta
            where uta.userSid=#{userSid} and uta.sid=#{sid} and uta.attention=1 and a.sid=uta.eid
            )
        </if>
        order by a.id asc
        LIMIT #{start} , #{size}
    </select>

    <select id="getTenantsCount" resultType="java.lang.Long">
        select count(DISTINCT(a.sid))
        from tenant a
        left join tenant_contract b on a.sid=b.eid
        left join supplier_tenant_map c on a.sid=c.eid
        where 1=1 and c.sid=#{sid}
        <if test="id != '' and name != ''">
            AND (a.id like "%"#{id}"%" or a.name like "%"#{name}"%")
        </if>
        <if test="eid != 0">
            AND a.sid = #{eid}
        </if>
        <if test="serviceType != null">
            AND b.serviceType=#{serviceType}
        </if>
        <if test="status != null">
            AND a.status=#{status}
        </if>
        <if test="contractExpiryDate != null">
            AND b.contractExpiryDate <![CDATA[<=]]>#{contractExpiryDate}
        </if>
        <if test="role!='superManager'">
            and exists(select eid from tenant_contract tc where tc.sid = c.sid and tc.productCode IN
            <foreach collection="productCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and tc.eid = c.eid)
        </if>
        <if test="attention == true">
            and exists(select 1 from user_tenant_attention uta where uta.sid = #{sid} AND uta.attention=#{attention}
            and uta.eid = a.sid and uta.userSid = #{userSid})
        </if>
        <if test="attention == false">
            and not exists(select 1 from user_tenant_attention uta
            where uta.userSid=#{userSid} and uta.sid=#{sid} and uta.attention=1 and a.sid=uta.eid
            )
        </if>
    </select>

    <select id="getTenantDetail" resultMap="TenantsMap">
        select a.sid,a.id,a.name,a.customer_id,a.taxCode,a.status,a.registerPhone,a.address,
        a.contacts,a.email,a.phone,a.cellphone_prefix,a.telephone,
        b.id as tc_id,b.eid as tc_eid,b.sid as tc_sid,b.productCode as tc_productCode,d.productShortName as tc_productShortName,b.serviceType as tc_serviceType,
        b.serviceStaffId as tc_serviceStaffId,b.serviceStaffName as tc_serviceStaffName,b.contractState as tc_contractState,b.contractStartDate as tc_contractStartDate,
        b.contractExpiryDate as tc_contractExpiryDate,b.industryId as tc_industryId,b.areaId as tc_areaId,b.access as tc_access,
        c.id as st_id,c.sid as st_sid,c.eid as st_eid,c.serviceCode as st_serviceCode,
        tmc.id as tmc_id,tmc.sid as tmc_sid,tmc.eid as tmc_eid,tmc.moduleId as tmc_moduleId,sam.moduleName as tmc_moduleName,tmc.status as tmc_status,tmc.startDate as tmc_startDate,
        tmc.endDate as tmc_endDate,tmc.serverCount as tmc_serverCount,tmc.workStationCount as tmc_workStationCount,tmc.iotCount as tmc_iotCount,
        tmc.staffId as tmc_staffId,tmc.openId as tmc_openId,tmc.userName as tmc_userName,tmc.noticeEmail as tmc_noticeEmail,
        CASE
        WHEN IFNULL(tmc.endDate,'')='' then null
        WHEN TIMESTAMPDIFF(DAY, NOW(), tmc.endDate)>=0 then 0
        else 1 END as tmc_expired
        from tenant a
        left join tenant_contract b on a.sid=b.eid
        left join supplier_tenant_map c on a.sid=c.eid
        left join supplier_product d on b.sid=d.sid and b.productCode=d.productCode
        left join tenant_module_contract tmc on a.sid=tmc.eid
        LEFT JOIN supplier_aiops_module sam ON sam.id = tmc.moduleId
        where a.sid=#{tenantSid} and c.sid=#{sid} and b.sid=#{sid}
    </select>

    <select id="getTenantDetailV2" resultMap="TenantsMap">
        select t.sid,t.id,t.name,t.customer_id,t.taxCode,t.status,t.registerPhone,t.address,
        t.contacts,t.email,t.phone,t.cellphone_prefix,t.telephone,
        tmc.id as tmc_id,tmc.sid as tmc_sid,tmc.eid as tmc_eid,tmc.moduleId as tmc_moduleId,sam.moduleName as
        tmc_moduleName,tmc.status as tmc_status,tmc.startDate as tmc_startDate,
        tmc.endDate as tmc_endDate,tmc.serverCount as tmc_serverCount,tmc.workStationCount as
        tmc_workStationCount,tmc.iotCount as tmc_iotCount,
        tmc.staffId as tmc_staffId,tmc.openId as tmc_openId,tmc.userName as tmc_userName,tmc.noticeEmail as
        tmc_noticeEmail,
        CASE
        WHEN IFNULL(tmc.endDate,'')='' then null
        WHEN TIMESTAMPDIFF(DAY, NOW(), tmc.endDate)>=0 then 0
        else 1 END as tmc_expired,tc.updateTime as tc_updateTime,tc.contractStartDate as tc_contractStartDate,tc.contractExpiryDate as tc_contractExpiryDate,tc.productCode as tc_productCode
        from tenant t
            left join tenant_contract tc on tc.eid = t.sid
        left join tenant_module_contract tmc on t.sid=tmc.eid
        LEFT JOIN supplier_aiops_module sam ON sam.id = tmc.moduleId
        where 1=1
        <foreach collection="tenantList" item="item" open=" AND  t.sid IN (" separator="," close=")">
            #{item.eid}
        </foreach>
        <foreach collection="tenantList" item="item" open=" AND  tmc.moduleId IN (" separator="," close=")">
            #{item.moduleId}
        </foreach>
    </select>
    <select id="getTenantV2" resultMap="TenantsMapV2">
        select a.sid,a.id,a.name,a.customer_id,a.taxCode,a.status,a.registerPhone,a.address,
        a.contacts,a.email,a.phone,a.cellphone_prefix,a.telephone,a.isv,s.sid as supplier_sid,s.supplierType as supplierType
        from tenant a
        left join supplier s on s.eid = a.sid
        where a.sid=#{sid}
    </select>

    <select id="getTenant" resultMap="TenantsMap">
        select a.sid,a.id,a.name,a.customerFullNameCH,a.customer_id,a.taxCode,a.status,a.registerPhone,a.address,
               a.contacts,a.email,a.phone,a.cellphone_prefix,a.telephone,a.isv
        from tenant a

        where a.sid=#{sid}
    </select>

    <select id="getTenantContract" resultType="com.digiwin.escloud.aiouser.model.tenant.TenantContract">
        select b.sid,b.eid,a.name tenantName, b.productCode,b.productCode ,sp.productCategory productShortName ,b.contractState,
               b.contractStartDate,b.contractExpiryDate,
               CASE 
                   WHEN TIMESTAMPDIFF(DAY, NOW(), b.contractExpiryDate) >= 0 THEN 'NORMAL'
                   ELSE 'EXPIRY'
               END as contractExpiry,
        b.productId,mcs.ServiceStaff as serviceStaffName,mcs.cust_level as custLevel
        from tenant a
        left join tenant_contract b on a.sid=b.eid
        LEFT JOIN supplier_product sp ON sp.sid = b.sid AND sp.productCode = b.productCode
        LEFT JOIN aio_contractstate ac ON ac.contractState= b.contractState
        LEFT JOIN mars_customerservice mcs on mcs.CustomerServiceCode = a.id and mcs.ProductCode = b.productCode
        where b.sid=#{sid} and b.eid=#{eid} and b.productCode=#{productCode}
        <if test="needUnExpired != null and needUnExpired==true">
            AND TIMESTAMPDIFF(DAY, NOW(), b.contractExpiryDate)>=0
        </if>
        <if test="contractStatus != null">
            <choose>
                <when test="contractStatus == 'EXPIRED'">
                    AND TIMESTAMPDIFF(DAY, NOW(), b.contractExpiryDate) &lt;= 0
                </when>
                <when test="contractStatus == 'UNEXPIRED'">
                    AND TIMESTAMPDIFF(DAY, NOW(), b.contractExpiryDate) >= 0
                </when>
                <when test="contractStatus == 'SOON_EXPIRE'">
                    AND TIMESTAMPDIFF(DAY, NOW(), b.contractExpiryDate) >= 0
                    AND TIMESTAMPDIFF(DAY, NOW(), b.contractExpiryDate) &lt;= 30
                </when>
            </choose>
        </if>
    </select>

    <update id="updateTCProductIdByProductCode">
        update tenant_contract tc set productId=#{productId}
        where tc.sid=#{sid} and tc.eid=#{eid} and tc.productCode=#{productCode}
    </update>

    <select id="selectTenantProductContractList" resultType="com.digiwin.escloud.aiouser.model.tenant.TenantContract">
        SELECT tc.sid, tc.eid, t.name AS tenantName, tc.productCode, sp.productShortName,
                 sp.productShortNameCN AS productShortNameCN, sp.productShortNameTW AS productShortNameTW,
                 sp.productShortNameUS AS productShortNameUS, sp.productShortNameVN AS productShortNameVN,
                 sp.productShortNameTH AS productShortNameTH,
                 tc.contractState, tc.contractStartDate, tc.contractExpiryDate
        FROM tenant t
        LEFT JOIN tenant_contract tc ON t.sid = tc.eid
        LEFT JOIN supplier_product sp ON tc.productCode = sp.productCode AND tc.sid =sp.sid
        WHERE tc.sid = #{sid} AND tc.eid = #{eid}
    </select>

    <select id="getTenantSimpleInfos" resultMap="TenantsMap">
        select a.sid,a.id,a.name,b.serviceCode as st_serviceCode,a.taxCode,b.sid as st_sid,a.status,b.eid as st_eid,b.id as st_id
        from tenant a
        left join supplier_tenant_map b on a.sid=b.eid
        where 1=1 and b.sid=#{sid}
        <choose>
            <when test="serviceCode != '' and name != '' and taxCode != ''">
                AND (b.serviceCode like "%"#{serviceCode}"%" or a.name like "%"#{name}"%" or a.taxCode like "%"#{taxCode}"%")
            </when>
            <otherwise>
                <if test="serviceCode != '' and name != ''">
                    AND (b.serviceCode like "%"#{serviceCode}"%" or a.name like "%"#{name}"%")
                </if>
            </otherwise>
        </choose>
        order by b.serviceCode
        <if test="size != 0">
            LIMIT #{start} , #{size}
        </if>
    </select>

    <select id="getTenantSimpleCount" resultType="java.lang.Long">
        select count(*)
        from tenant a
        left join supplier_tenant_map b on a.sid=b.eid
        where 1=1 and b.sid=#{sid}
        <choose>
            <when test="serviceCode != '' and name != '' and taxCode != ''">
                AND (b.serviceCode like "%"#{serviceCode}"%" or a.name like "%"#{name}"%" or a.taxCode like "%"#{taxCode}"%")
            </when>
            <otherwise>
                <if test="serviceCode != '' and name != ''">
                    AND (b.serviceCode like "%"#{serviceCode}"%" or a.name like "%"#{name}"%")
                </if>
            </otherwise>
        </choose>
    </select>

    <select id="getTenantUsers" resultMap="UsersMap">
        select b.sid,b.id,b.name,b.email,b.telephone,b.phone,b.status
        from user_tenant_map a
        left join user b on a.userSid=b.sid
        left join tenant c on c.sid=a.eid
        where a.eid=#{tenantSid} and b.status=1 and b.id!=c.id
        <if test="userName != ''">
            AND b.name like "%"#{userName}"%"
        </if>
        <if test="size != 0">
            LIMIT #{start} , #{size}
        </if>
    </select>

    <select id="getTenantUsersCount" resultType="java.lang.Long">
        select count(*)
        from user_tenant_map a
        left join user b on a.userSid=b.sid
        left join tenant c on c.sid=a.eid
        where a.eid=#{tenantSid} and b.status=1 and b.id!=c.id
        <if test="userName != ''">
            AND b.name like "%"#{userName}"%"
        </if>
    </select>

    <update id="updateTenant">
        UPDATE tenant_contract SET description = #{description} WHERE eid = #{eid} AND productCode = #{productCode}
    </update>
    <select id="getContractStateList" resultType="com.digiwin.escloud.aiouser.model.tenant.ContractState">
        select * from aio_contractstate a order by a.id
    </select>

    <select id="getTenantByServiceCode" resultMap="TenantsMap">
        select a.sid,a.id,a.name,a.customer_id,a.taxCode,a.status,a.registerPhone,a.address,a.contacts,a.email,a.phone,a.cellphone_prefix,
        a.telephone,b.id as st_id,b.sid as st_sid,b.eid as st_eid,b.serviceCode as st_serviceCode
        from tenant a
        left join supplier_tenant_map b on a.sid=b.eid
        where b.serviceCode=#{serviceCode}
        limit 1
    </select>

    <select id="getTenantDetailByServiceCode" resultType="com.digiwin.escloud.aiouser.model.tenant.Tenant">
        select a.sid,a.id,a.name,a.customer_id,a.taxCode,a.status,a.registerPhone,a.address,a.contacts,a.email,a.phone,a.cellphone_prefix,
        a.telephone
        from tenant a
        left join supplier_tenant_map b on a.sid=b.eid
        LEFT JOIN supplier s on s.sid = b.sid
        where b.serviceCode = #{serviceCode}
        limit 1
    </select>
    <insert id="insertTenant" keyProperty="sid" keyColumn="sid" parameterType="com.digiwin.escloud.aiouser.model.tenant.Tenant">
        insert into tenant(sid, id, name, customer_id,taxCode,status,registerPhone,address,contacts,email,
        phone,cellphone_prefix,telephone)
        values(#{sid}, #{id}, #{name}, #{customer_id}, #{taxCode}, #{status},#{registerPhone},#{address},#{contacts},#{email},
        #{phone},#{cellphone_prefix},#{telephone})
    </insert>

    <insert id="insertTenantContract" parameterType="com.digiwin.escloud.aiouser.model.tenant.TenantContract">
        insert into tenant_contract(id,eid,sid,productCode,productShortName,serviceType,serviceStaffId,serviceStaffName,contractState,
        contractStartDate,contractExpiryDate,industryId,areaId,access,description,canContact,IsTrial)
        values (#{id},#{eid},#{sid},#{productCode},#{productShortName},#{serviceType},#{serviceStaffId},#{serviceStaffName},#{contractState},
        #{contractStartDate},#{contractExpiryDate},#{industryId},#{areaId},#{access},#{description},#{canContact},#{isTrial})
    </insert>

    <insert id="updateTenantContract" parameterType="com.digiwin.escloud.aiouser.model.tenant.TenantContract">
        update tenant_contract set serviceType=#{serviceType},serviceStaffId=#{serviceStaffId},serviceStaffName=#{serviceStaffName},
        contractState=#{contractState},contractStartDate=#{contractStartDate},contractExpiryDate=#{contractExpiryDate},industryId=#{industryId},
        areaId=#{areaId},access=#{access},description=#{description},canContact=#{canContact},IsTrial=#{isTrial}
        where sid=#{sid} and eid=#{eid} and productCode=#{productCode}
    </insert>

    <select id="getSearchTenantList" resultType="com.digiwin.escloud.aiouser.model.tenant.TenantInfo">
        select dp_t_main.sid as sid, dp_t_main.eid as eid, t.id as id, t.name as name,ifnull(t.customerFullNameCH,t.name)
        customerFullNameCH,t.customerFullNameEN,dp_t_main.serviceCode as serviceCode
        FROM supplier_tenant_map dp_t_main
        LEFT JOIN tenant t ON t.sid = dp_t_main.eid
        <if test="productCode != null and productCode  !=  '' ">
            inner join tenant_contract tc on t.sid=tc.eid
        </if>
        WHERE 1=1 AND dp_t_main.sid = #{sid}
        <if test="tenantSearchContent != null and tenantSearchContent  !=  '' ">
            AND (dp_t_main.eid LIKE CONCAT("%",#{tenantSearchContent},"%") OR t.name LIKE
            CONCAT("%",#{tenantSearchContent},"%") OR t.customerFullNameCH LIKE CONCAT("%",#{tenantSearchContent},"%")
            OR t.customerFullNameEN LIKE CONCAT("%",#{tenantSearchContent},"%") OR dp_t_main.serviceCode LIKE
            CONCAT("%",#{tenantSearchContent},"%"))
        </if>
        <if test="platformType == 'AIEOM_SERVICE'">
           AND dp_t_main.eid IN (
            SELECT DISTINCT tmc.eid
            FROM tenant_module_contract tmc
            LEFT JOIN supplier_tenant_map stm ON tmc.eid = stm.eid
            WHERE serviceIsvSid = #{serviceIsvSid} AND stm.sid = #{sid}
            )
        </if>
        <if test="needAuth ==true">
            <if test="role!='superManager'">
                <if test="role=='customerServiceSupervisor'">
                    and exists(select 1 from user_tenant_attention uta
                    inner join supplier_employee se on uta.sid=se.sid AND uta.userSid=se.userSid
                    where uta.sid = #{sid} AND uta.attention=1 and se.orgSid=#{orgSid}
                    and uta.eid = t.sid)
                </if>
                <if test="role=='customerService'">
                    and exists(select 1 from user_tenant_attention uta where uta.sid = #{sid} AND uta.attention=1
                    and uta.eid = t.sid and uta.userSid = #{userSid})
                </if>
            </if>
        </if>
        <if test="productCode != null and productCode != '' ">
            AND tc.sid = #{sid} and tc.productCode=#{productCode}
        </if>
    </select>

    <select id="selectTenantId" resultType="java.lang.Long">
        SELECT sid
        FROM tenant
        WHERE sid = #{tenantId}
    </select>

    <select id="selectTenantNameList" resultType="com.digiwin.escloud.aiouser.model.tenant.TenantNameInfo">
        SELECT sid AS eid, name
        FROM tenant
        WHERE 1 != 1
        <if test="tenantIdList != null">
            <foreach collection="tenantIdList" item="item" open=" OR sid IN(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectTenantNameAndServiceCodeList" resultType="com.digiwin.escloud.aiouser.model.tenant.TenantMapDTO">

        SELECT stm.serviceCode serviceCode,t.sid as eid, t.name name,t.customerFullNameCH fullName FROM tenant t
        inner JOIN supplier_tenant_map stm ON t.sid = stm.eid
        where 1=1
        <if test="tenantIdList != null and tenantIdList.size()>0">
            <foreach collection="tenantIdList" item="item" index="index" separator="," open="AND t.sid in(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getAuthorizedTenantSids" resultType="java.lang.Long">
        SELECT distinct(eid)
        FROM tenant_contract
        WHERE sid=#{sid}
        <if test="authorizedProductCodes != null and authorizedProductCodes.size()>0">
            and productCode IN
            <foreach collection="authorizedProductCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getTenantExistedList" resultType="com.digiwin.escloud.aiouser.model.tenant.TenantInfo">
        select stm.sid as sid, stm.eid as eid, t.name as name, stm.serviceCode as serviceCode
        FROM supplier_tenant_map stm
        inner JOIN tenant t ON t.sid = stm.eid
        WHERE 1=1 AND stm.sid = #{sid}
        <if test="content != null and content  !=  '' ">
            AND (stm.eid LIKE CONCAT("%",#{content},"%") OR t.name LIKE CONCAT("%",#{content},"%") OR stm.serviceCode LIKE CONCAT("%",#{content},"%"))
        </if>
        <if test="role!='superManager'">
            and exists(select eid from tenant_contract tc where tc.sid = stm.sid and tc.productCode IN
            <foreach collection="productCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and tc.eid = stm.eid)
        </if>
        and not exists(select 1 from user_tenant_attention uta
        where uta.userSid=#{userSid} and uta.sid=#{sid} and uta.attention=1 and t.sid=uta.eid
        )
        order by stm.serviceCode
        LIMIT #{start} , #{size}
    </select>

    <select id="getTenantExistedCount" resultType="java.lang.Integer">
        select count(*)
        FROM supplier_tenant_map stm
        inner JOIN tenant t ON t.sid = stm.eid
        WHERE 1=1 AND stm.sid = #{sid}
        <if test="content != null and content  !=  '' ">
            AND (stm.eid LIKE CONCAT("%",#{content},"%") OR t.name LIKE CONCAT("%",#{content},"%") OR stm.serviceCode LIKE CONCAT("%",#{content},"%"))
        </if>
        <if test="role!='superManager'">
            and exists(select eid from tenant_contract tc where tc.sid = stm.sid and tc.productCode IN
            <foreach collection="productCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and tc.eid = stm.eid)
        </if>
        and not exists(select 1 from user_tenant_attention uta
        where uta.userSid=#{userSid} and uta.sid=#{sid} and uta.attention=1 and t.sid=uta.eid
        )
    </select>

    <select id="getTenantSidByServiceCode" resultType="java.lang.Long">
        SELECT eid
        FROM supplier_tenant_map
        WHERE serviceCode = #{serviceCode}
        limit 1;
    </select>

    <select id="getSupplierTenantMapListByMap"
            resultType="com.digiwin.escloud.aiouser.model.supplier.SupplierTenantMap">
        SELECT stm.sid, stm.eid, stm.serviceCode,
               t.name
        FROM supplier_tenant_map stm
        LEFT JOIN tenant t ON stm.eid = t.sid
        <where>
            <if test="eidList != null">
                <foreach collection="eidList" item="item" open=" AND stm.eid IN(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="serviceCodeList != null">
                <foreach collection="serviceCodeList" item="item" open=" AND stm.serviceCode IN(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getTenantNameByServiceCode" resultType="java.util.Map">
        SELECT id, name
        FROM tenant
        WHERE id like #{serviceCode}
        limit 15;
    </select>


    <select id="getTenantModuleCountByServiceCode" resultType="java.lang.Integer">
        SELECT count(*)
        FROM supplier_tenant_map stm
        LEFT JOIN tenant_module_contract tmc ON stm.eid=tmc.eid And (tmc.moduleid IS NOT NULL AND tmc.moduleid !='' AND tmc.moduleid !='0') AND (tmc.status IS NOT NULL AND tmc.status !='' AND tmc.status !='0')
        WHERE stm.serviceCode = #{serviceCode}
    </select>

    <select id="getTenantModuleContracts" resultMap="TenantsMap">
        SELECT main.*,
               tmc.id AS tmc_id, tmc.sid AS tmc_sid, tmc.eid AS tmc_eid, tmc.moduleId AS tmc_moduleId,
               tmc.status AS tmc_status, tmc.startDate AS tmc_startDate, tmc.endDate AS tmc_endDate,
               tmc.staffId AS tmc_staffId, se.name AS tmc_staffName, tmc.openId AS tmc_openId,
               tmc.userName AS tmc_userName, tmc.noticeEmail AS tmc_noticeEmail,tmc.itemNo AS tmc_itemNo,
               tmc.serverCount AS tmc_serverCount, tmc.workStationCount AS tmc_workStationCount,
               tmc.iotCount AS tmc_iotCount,tmc.serviceIsvSid AS tmc_serviceIsvSid,tmc.serviceIsvName AS tmc_serviceIsvName,
               CASE WHEN IFNULL(tmc.endDate,'')='' THEN NULL
               WHEN TIMESTAMPDIFF(DAY, NOW(), tmc.endDate)>=0 THEN 0
               ELSE 1 END AS tmc_expired,
               sam.moduleCode AS tmc_moduleCode, sam.moduleName AS tmc_moduleName,
               tmc.tmc_tmcd_id, tmc.tmc_tmcd_tmcId, tmc.tmc_tmcd_samcId,
               tmc.tmc_tmcd_availableCount, tmc.tmc_tmcd_usedCount, tmc.tmc_tmcd_isContainHoldAuth,
               tmc.tmc_tmcd_classCode, tmc.tmc_tmcd_className,
               tmc.tmc_tmcd_className_CN, tmc.tmc_tmcd_className_TW,
               stm.id AS st_id, stm.sid AS st_sid, stm.eid AS st_eid, stm.serviceCode AS st_serviceCode,
               uta.attention, uta.receiveWarningMail
        FROM (
            SELECT DISTINCT dp_t_main.sid, dp_t_main.id, dp_t_main.name, dp_t_main.customer_id, dp_t_main.taxCode, dp_t_main.status, dp_t_main.registerPhone,
                            dp_t_main.address, dp_t_main.contacts, dp_t_main.email, dp_t_main.phone, dp_t_main.cellphone_prefix, dp_t_main.telephone, dp_t_main.customerFullNameCH
            FROM tenant dp_t_main
            LEFT JOIN tenant_module_contract tmc ON dp_t_main.sid=tmc.eid
            LEFT JOIN supplier_aiops_module sam ON tmc.moduleId=sam.id
            LEFT JOIN supplier_tenant_map stm ON dp_t_main.sid=stm.eid
            LEFT JOIN user_tenant_attention uta ON dp_t_main.sid=uta.eid AND uta.userSid=#{userSid}
            WHERE 1=1 AND stm.sid=#{sid}
            <if test="statusList != null">
                <foreach collection="statusList" item="item" open=" AND tmc.status IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="moduleIdList != null">
                <foreach collection="moduleIdList" item="item" open=" AND tmc.moduleId IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="content != null and content != ''">
                AND (dp_t_main.id LIKE "%"#{content}"%" OR dp_t_main.name LIKE "%"#{content}"%" OR dp_t_main.customerFullNameCH LIKE "%"#{content}"%")
            </if>
            <if test="endDate != null">
                AND tmc.endDate <![CDATA[<=]]>#{endDate}
            </if>
            <choose>
                <when test="attention != null and attention">
                    AND EXISTS(SELECT 1 FROM user_tenant_attention
                               WHERE sid=#{sid} AND attention=#{attention} AND eid=dp_t_main.sid AND userSid=#{userSid})
                </when>
                <when test="attention != null and !attention">
                    AND NOT EXISTS(SELECT 1 FROM user_tenant_attention
                                   WHERE userSid=#{userSid} AND sid=#{sid} AND attention=1 AND dp_t_main.sid=eid)
                </when>
            </choose>
            <if test="serviceIsvSids != null and serviceIsvSids.length > 0">
                <foreach collection="serviceIsvSids" item="item" open=" AND tmc.serviceIsvSid IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            ORDER BY dp_t_main.id ASC
            LIMIT #{start}, #{size}
        ) main
        LEFT JOIN (
            SELECT tmc.*,supplier.name AS serviceIsvName,
                   tmcd.id AS tmc_tmcd_id, tmcd.tmcId AS tmc_tmcd_tmcId, tmcd.samcId AS tmc_tmcd_samcId,
                   CASE WHEN samc.id IS NULL THEN NULL
                   ELSE IFNULL(tmcd.availableCount, 0)
                   END AS tmc_tmcd_availableCount,
                   CASE WHEN samc.id IS NULL THEN NULL
                   ELSE IFNULL(tmcd.usedCount, 0)
                   END AS tmc_tmcd_usedCount,
                   EXISTS (SELECT 1 FROM supplier_aiops_module_class_detail samcd
                           WHERE samcd.samcId = samc.id AND holdAuth = 1) AS tmc_tmcd_isContainHoldAuth,
                   samc.classCode AS tmc_tmcd_classCode, samc.className AS tmc_tmcd_className,
                   samc.className_CN AS tmc_tmcd_className_CN, samc.className_TW AS tmc_tmcd_className_TW
            FROM tenant_module_contract tmc
            LEFT JOIN supplier_aiops_module_class samc ON tmc.moduleId = samc.samId
            LEFT JOIN tenant_module_contract_detail tmcd ON samc.id = tmcd.samcId AND tmc.id = tmcd.tmcId
            LEFT JOIN supplier supplier ON supplier.sid = tmc.serviceIsvSid
            WHERE tmc.sid=#{sid}
            <if test="statusList != null">
                <foreach collection="statusList" item="item" open=" AND tmc.status IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="moduleIdList != null">
                <foreach collection="moduleIdList" item="item" open=" AND tmc.moduleId IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="endDate != null">
                AND tmc.endDate <![CDATA[<=]]>#{endDate}
            </if>
            <if test="serviceIsvSids != null and serviceIsvSids.length > 0">
                <foreach collection="serviceIsvSids" item="item" open=" AND tmc.serviceIsvSid IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        ) tmc ON main.sid=tmc.eid
        LEFT JOIN supplier_aiops_module sam ON tmc.moduleId=sam.id
        LEFT JOIN supplier_employee se ON tmc.serviceIsvSid = se.sid AND tmc.staffId=se.userSid
        LEFT JOIN supplier_tenant_map stm ON main.sid=stm.eid
        LEFT JOIN user_tenant_attention uta ON main.sid=uta.eid AND uta.userSid=#{userSid}
        WHERE 1=1 AND stm.sid=#{sid}
        <!--        <if test="status != null">-->
        <!--            AND tmc.status = #{status}-->
        <!--        </if>-->
        <!--        <if test="moduleId != null">-->
        <!--            AND tmc.moduleId = #{moduleId}-->
        <!--        </if>-->
        <!--        <if test="content != null AND content != ''">-->
        <!--            AND (t.id LIKE "%"#{content}"%" or t.name LIKE "%"#{content}"%")-->
        <!--        </if>-->
        <!--        <if test="endDate != null">-->
        <!--            AND tmc.endDate <![CDATA[<=]]>#{endDate}-->
        <!--        </if>-->
        <!--        <if test="attention == true">-->
        <!--            AND exists(SELECT 1 FROM user_tenant_attention uta WHERE uta.sid = #{sid} AND uta.attention=#{attention}-->
        <!--            AND uta.eid = t.sid AND uta.userSid = #{userSid})-->
        <!--        </if>-->
        <!--        <if test="attention == false">-->
        <!--            AND not exists(SELECT 1 FROM user_tenant_attention uta-->
        <!--            WHERE uta.userSid=#{userSid} AND uta.sid=#{sid} AND uta.attention=1 AND t.sid=uta.eid-->
        <!--            )-->
        <!--        </if>-->
        ORDER BY main.id ASC, tmc.updateTime DESC
    </select>

    <select id="getTenantModuleContractsCount" resultType="java.lang.Integer">
        SELECT count(DISTINCT(t.sid))
        FROM tenant t
        LEFT JOIN tenant_module_contract tmc ON t.sid=tmc.eid
        LEFT JOIN supplier_aiops_module sam ON tmc.moduleId=sam.id
        LEFT JOIN supplier_tenant_map stm ON t.sid=stm.eid
        LEFT JOIN user_tenant_attention uta ON t.sid=uta.eid AND uta.userSid=#{userSid}
        WHERE stm.sid=#{sid}
        <if test="statusList != null">
            <foreach collection="statusList" item="item" open=" AND tmc.status IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        <if test="moduleIdList != null">
            <foreach collection="moduleIdList" item="item" open=" AND tmc.moduleId IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        <if test="content != null and content != ''">
            AND (t.id LIKE "%"#{content}"%" OR t.name LIKE "%"#{content}"%")
        </if>
        <if test="endDate != null">
            AND tmc.endDate <![CDATA[<=]]>#{endDate}
        </if>
        <choose>
            <when test="attention != null and attention">
                AND EXISTS(SELECT 1 FROM user_tenant_attention uta2
                           WHERE uta2.sid=#{sid} AND uta2.attention=#{attention} AND uta2.userSid=#{userSid}
                               AND uta2.eid=t.sid)
            </when>
            <when test="attention != null and !attention">
                AND NOT EXISTS(SELECT 1 FROM user_tenant_attention uta2
                               WHERE uta2.sid=#{sid} AND uta2.attention=1 AND uta2.userSid=#{userSid}
                                    AND uta2.eid=t.sid)
            </when>
        </choose>
        <if test="serviceIsvSids != null and serviceIsvSids.length > 0">
            <foreach collection="serviceIsvSids" item="item" open=" AND tmc.serviceIsvSid IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>
    
    <select id="selectTenantModuleContractDetailOnly" resultMap="TenantModuleContractDetailMap">
        SELECT tmcd.*, EXISTS (SELECT 1 FROM supplier_aiops_module_class_detail samcd
                          WHERE samcd.samcId = tmcd.samcId AND holdAuth = 1) AS isContainHoldAuth
        FROM tenant_module_contract_detail tmcd
        WHERE tmcd.tmcId=#{tmcId}
    </select>

    <insert id="saveTenantModuleContracts" keyProperty="id" keyColumn="id">
        INSERT INTO tenant_module_contract(id, sid, eid, moduleId, serviceIsvSid, status, orderMethod, startDate, endDate,
                                           serverCount, workStationCount, iotCount, staffId, openId,
                                           userName, noticeEmail, itemNo)
        <foreach collection="tenantModuleContracts" open="VALUES" item="item" separator=",">
            (#{item.id}, #{item.sid}, #{item.eid}, #{item.moduleId}, #{item.serviceIsvSid}, #{item.status}, #{item.orderMethod}, #{item.startDate}, #{item.endDate},
             #{item.serverCount}, #{item.workStationCount}, #{item.iotCount}, #{item.staffId}, #{item.openId},
             #{item.userName}, #{item.noticeEmail}, #{item.itemNo})
        </foreach>
        ON DUPLICATE KEY UPDATE moduleId = VALUES(moduleId), serviceIsvSid = VALUES(serviceIsvSid), status = VALUES(status), startDate = VALUES(startDate),
                                endDate = VALUES(endDate), serverCount = VALUES(serverCount),
                                workStationCount = VALUES(workStationCount), iotCount = VALUES(iotCount),
                                staffId = VALUES(staffId), orderMethod = VALUES(orderMethod), itemNo = VALUES(itemNo)
        <foreach collection="tenantModuleContracts" item="item">
            <if test="item.userName != '' and item.userName!=null">
                , userName = #{item.userName}
            </if>
            <if test="item.noticeEmail != '' and item.noticeEmail!=null">
                , noticeEmail = #{item.noticeEmail}
            </if>
        </foreach>
    </insert>

    <insert id="saveTenantModuleContractsBySync" keyProperty="id" keyColumn="id">
        INSERT INTO tenant_module_contract(id, sid, eid, moduleId, status, orderMethod, startDate, endDate,
        serverCount, workStationCount, iotCount, staffId, openId,
        userName, noticeEmail)
        <foreach collection="tenantModuleContracts" open="VALUES" item="item" separator=",">
            (#{item.id}, #{item.sid}, #{item.eid}, #{item.moduleId}, #{item.status},
            CASE WHEN IFNULL(#{item.endDate},'')='' THEN NULL
            WHEN TIMESTAMPDIFF(DAY, NOW(), #{item.endDate})>=0 THEN 1
            ELSE 4 END,
            #{item.startDate}, #{item.endDate},
            #{item.serverCount}, #{item.workStationCount}, #{item.iotCount}, #{item.staffId}, #{item.openId},
            #{item.userName}, #{item.noticeEmail})
        </foreach>
        ON DUPLICATE KEY UPDATE moduleId = VALUES(moduleId), status = VALUES(status), startDate = VALUES(startDate),
                                endDate = VALUES(endDate), serverCount = VALUES(serverCount),
                                workStationCount = VALUES(workStationCount), iotCount = VALUES(iotCount),
                                staffId = VALUES(staffId), orderMethod = 4
        <foreach collection="tenantModuleContracts" item="item">
            <if test="item.userName != '' and item.userName!=null">
                , userName = #{item.userName}
            </if>
            <if test="item.noticeEmail != '' and item.noticeEmail!=null">
                , noticeEmail = #{item.noticeEmail}
            </if>
        </foreach>
    </insert>

    <insert id="saveTenantModuleContractHistories" keyProperty="id" keyColumn="id"
            parameterType="com.digiwin.escloud.aiouser.model.tenant.TenantModuleContractHistory">
        INSERT INTO tenant_module_contract_history(id, moduleContractId,sid, eid, moduleId,serviceIsvSid,status,operateType, orderMethod,
                                                   startDate, endDate, serverCount, workStationCount, iotCount,
                                                   staffId, openId, userName, noticeEmail, itemNo, operatorName)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.moduleContractId}, #{item.sid}, #{item.eid}, #{item.moduleId}, #{item.serviceIsvSid}, #{item.status},#{item.operateType}, #{item.orderMethod},
             #{item.startDate}, #{item.endDate}, #{item.serverCount}, #{item.workStationCount}, #{item.iotCount},
             #{item.staffId}, #{item.openId}, #{item.userName}, #{item.noticeEmail}, #{item.itemNo}, #{item.operatorName})
        </foreach>
    </insert>

    <insert id="saveTenantModuleContractHistoriesBySync" keyProperty="id" keyColumn="id"
            parameterType="com.digiwin.escloud.aiouser.model.tenant.TenantModuleContractHistory">
        INSERT INTO tenant_module_contract_history(id, moduleContractId,sid, eid, moduleId,status,operateType,orderMethod,
        startDate, endDate, serverCount, workStationCount, iotCount,
        staffId, openId, userName, noticeEmail,operatorName)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.moduleContractId}, #{item.sid}, #{item.eid}, #{item.moduleId}, #{item.status},#{item.operateType},
            CASE WHEN IFNULL(#{item.endDate},'')='' THEN NULL
            WHEN TIMESTAMPDIFF(DAY, NOW(), #{item.endDate})>=0 THEN 1
            ELSE 4 END,
            #{item.startDate}, #{item.endDate}, #{item.serverCount}, #{item.workStationCount}, #{item.iotCount},
            #{item.staffId}, #{item.openId}, #{item.userName}, #{item.noticeEmail}, #{item.operatorName})
        </foreach>
    </insert>

    <insert id="saveTenantModuleContractDetailList"
            parameterType="com.digiwin.escloud.aiouser.model.tenant.TenantModuleContractDetail">
        INSERT INTO tenant_module_contract_detail(id, tmcId, samcId, availableCount, usedCount)
        <foreach collection="list" item="item" open="VALUES(" separator="), (" close=")">
            #{item.id}, #{item.tmcId}, #{item.samcId}, #{item.availableCount}, #{item.usedCount}
        </foreach>
        ON DUPLICATE KEY UPDATE availableCount = (CASE WHEN IFNULL(usedCount, 0) > VALUES(availableCount) THEN availableCount
                                                  ELSE VALUES(availableCount) END)
    </insert>

    <insert id="saveTenantModuleContractDetailListV2"
            parameterType="com.digiwin.escloud.aiouser.model.tenant.TenantModuleContractDetail">
        INSERT INTO tenant_module_contract_detail(id, tmcId, samcId, availableCount, usedCount)
        <foreach collection="list" item="item" open="VALUES(" separator="), (" close=")">
            #{item.id}, #{item.tmcId}, #{item.samcId}, #{item.availableCount}, #{item.usedCount}
        </foreach>
        ON DUPLICATE KEY UPDATE
        availableCount = CASE
        WHEN IFNULL(usedCount, 0) > VALUES(availableCount) THEN availableCount
        ELSE VALUES(availableCount)
        END,
        usedCount = CASE
        WHEN IFNULL(usedCount, 0) > VALUES(availableCount) THEN VALUES(availableCount)
        ELSE VALUES(usedCount)
        END
    </insert>
    <delete id="deleteTenantModuleContractDetailByTmcId">
        DELETE FROM tenant_module_contract_detail
        WHERE tmcId=#{tmcId}
    </delete>

    <delete id="deleteTenantModuleContractDetailByTmcdIdList" parameterType="java.lang.Long">
        DELETE FROM tenant_module_contract_detail
        WHERE 1!=1
        <if test="tmcdIdList != null">
            <foreach collection="tmcdIdList" item="item" open=" OR id IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </delete>
    
    <select id="getUnExpiredTenantModuleContracts" resultMap="TenantsMap">
        <!--考虑到，可能合约中异动了合约结束日期小于今天，因此当remainDay<0时，多检查日期差小于0的合约(这些合约也要进行失效)-->
        SELECT main.*, tmc.id AS tmc_id, tmc.sid AS tmc_sid, tmc.eid AS tmc_eid, tmc.moduleId AS tmc_moduleId,
               sam.moduleName AS tmc_moduleName,
               tmc.status AS tmc_status, tmc.startDate AS tmc_startDate, tmc.endDate AS tmc_endDate,
               tmc.serverCount AS tmc_serverCount, tmc.workStationCount AS tmc_workStationCount,
               tmc.iotCount AS tmc_iotCount, tmc.staffId AS tmc_staffId, tmc.userName AS tmc_userName,
               tmc.noticeEmail AS tmc_noticeEmail, se.name AS tmc_staffName, se.email AS tmc_staffEmail,
               tmc.openId AS tmc_openId,
               tmcd.id AS tmc_tmcd_id, tmcd.tmcId AS tmc_tmcd_tmcId, tmcd.samcId AS tmc_tmcd_samcId,
               tmcd.availableCount AS tmc_tmcd_availableCount, tmcd.usedCount AS tmc_tmcd_usedCount,
               CASE WHEN TIMESTAMPDIFF(DAY, NOW(), tmc.endDate)>=0 THEN 0 ELSE 1 END AS tmc_expired,
               ifnull(DATEDIFF(tmc.endDate, CURDATE()), 0) as tmc_daysUntilExpiration,
               stm.id AS st_id, stm.sid AS st_sid, stm.eid AS st_eid, stm.serviceCode AS st_serviceCode
        FROM (
            SELECT DISTINCT t.sid, t.id, t.name, t.customer_id, t.taxCode, t.status, t.registerPhone, t.address,
                            t.contacts, t.email, t.phone, t.cellphone_prefix, t.telephone
            FROM tenant t
            LEFT JOIN tenant_module_contract tmc ON t.sid=tmc.eid
            LEFT JOIN supplier_aiops_module sam ON sam.id=tmc.moduleId
            LEFT JOIN supplier_tenant_map stm ON t.sid=stm.eid
            WHERE tmc.status=#{status}
            <choose>
                <when test="remainDay &lt; 0">
                    AND TIMESTAMPDIFF(DAY, DATE_FORMAT(NOW(), '%Y-%m-%d'), DATE_FORMAT(tmc.endDate, '%Y-%m-%d'))
                        <![CDATA[<]]>0
                </when>
                <otherwise>
                    AND TIMESTAMPDIFF(DAY, DATE_FORMAT(NOW(), '%Y-%m-%d'), DATE_FORMAT(tmc.endDate, '%Y-%m-%d'))
                        =#{remainDay}
                </otherwise>
            </choose>
        ) main
        LEFT JOIN tenant_module_contract tmc ON main.sid=tmc.eid
        LEFT JOIN tenant_module_contract_detail tmcd ON tmc.id = tmcd.tmcId
        LEFT JOIN supplier_aiops_module sam ON sam.id=tmc.moduleId
        LEFT JOIN supplier_employee se ON tmc.sid=se.sid AND tmc.staffId=se.userSid
        LEFT JOIN supplier_tenant_map stm on main.sid=stm.eid
        WHERE tmc.status=#{status}
        <choose>
            <when test="remainDay &lt; 0">
                AND TIMESTAMPDIFF(DAY, DATE_FORMAT(NOW(), '%Y-%m-%d'), DATE_FORMAT(tmc.endDate, '%Y-%m-%d'))
                    <![CDATA[<]]>0
            </when>
            <otherwise>
                AND TIMESTAMPDIFF(DAY, DATE_FORMAT(NOW(), '%Y-%m-%d'), DATE_FORMAT(tmc.endDate, '%Y-%m-%d'))
                    =#{remainDay}
            </otherwise>
        </choose>
    </select>

    <select id="getTenantModuleContractHistories" resultType="com.digiwin.escloud.aiouser.model.tenant.TenantModuleContractHistory">
        select id, moduleContractId,sid, eid, moduleId,serviceIsvSid,status,operateType,startDate,endDate,serverCount,workStationCount,iotCount,staffId,openId,userName,noticeEmail
        from tenant_module_contract_history
        where moduleContractId=#{moduleContractId}
        order by createTime desc
    </select>

    <select id="selectAllTenantModuleContractHistories" resultType="com.digiwin.escloud.aiouser.model.tenant.TenantModuleContractHistory">
        select id, moduleContractId,sid, eid, moduleId,serviceIsvSid,status,operateType,startDate,endDate,serverCount,workStationCount,iotCount,staffId,openId,userName,noticeEmail
        ,createTime
        from tenant_module_contract_history
        order by createTime
    </select>

    <update id="updateTenantModuleContractStatus">
        UPDATE tenant_module_contract
        SET status = #{status},orderMethod = #{moduleOrderMethod}
        WHERE 1 != 1
        <if test="idList != null">
            <foreach collection="idList" item="item" open=" OR id IN(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <select id="selectExistTenantModuleContract" resultMap="TenantModuleContractMap">
        SELECT tmc.*, sam.moduleCode
        FROM tenant_module_contract tmc
        LEFT JOIN supplier_aiops_module sam ON sam.sid=#{sid} AND tmc.moduleId=sam.id
        WHERE tmc.sid=#{sid} AND tmc.eid=#{eid}
        <if test="samIdList != null">
            <foreach collection="samIdList" item="item" open=" AND tmc.moduleId IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectTenantAllModuleContractDetail" resultMap="TenantModuleContractDetailMap">
        SELECT tmcd.*,
        samc.classCode AS classCode, samc.className AS className,
        samc.className_CN AS className_CN, samc.className_TW AS className_TW,
        IF(EXISTS (
        SELECT 1
        FROM supplier_aiops_module_class_detail samcd
        WHERE samcd.samcId = tmcd.samcId
        AND samcd.holdAuth = true
        ), true, false) AS isContainHoldAuth
        FROM tenant_module_contract tmc
        LEFT JOIN tenant_module_contract_detail tmcd ON tmc.id=tmcd.tmcId
        LEFT JOIN supplier_aiops_module_class samc ON tmcd.samcId=samc.id
        <if test="moduleCodeList != null">
            INNER JOIN supplier_aiops_module sam ON samc.samId = sam.id
            <foreach collection="moduleCodeList" item="item" open=" AND moduleCode IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        WHERE tmc.sid=#{sid} AND tmc.eid=#{eid} AND TIMESTAMPDIFF(DAY, NOW(), endDate)>=0
    </select>
    
    <select id="selectTenantModuleContractClassDetailByAiopsItem" resultMap="TenantModuleContractDetailMap">
        SELECT tmcd.*,
               EXISTS (SELECT 1 FROM supplier_aiops_module_class_detail samcd
                       WHERE samcd.samcId = samc.id AND holdAuth = 1) AS isContainHoldAuth,
               samcd.independentAuth,
               samc.classCode AS classCode, samc.className AS className,
               samc.className_CN AS className_CN, samc.className_TW AS className_TW,
               samcd.id AS samcd_id, samcd.samcId AS samcd_samcId,
               samcd.aiopsItemType AS samcd_aiopsItemType, samcd.aiopsItem AS samcd_aiopsItem,
               samcd.holdAuth AS samcd_holdAuth, samcd.isAuthByInstance AS samcd_isAuthByInstance,
               samcd.execParamsModelCode AS samcd_execParamsModelCode,
               samcd.allowDuplicateMapping AS samcd_allowDuplicateMapping
        FROM supplier_aiops_module_class_detail samcd
        INNER JOIN supplier_aiops_module_class samc ON samc.sid=#{sid} AND samcd.samcId=samc.id
        LEFT JOIN (
            SELECT tmcd.*,
                   (TIMESTAMPDIFF(DAY, NOW(), tmc.endDate)>=0) AS contractIsValid
            FROM tenant_module_contract_detail tmcd
            INNER JOIN tenant_module_contract tmc ON tmc.sid=#{sid} AND tmc.eid=#{eid} AND tmcd.tmcId=tmc.id
        ) tmcd ON samc.id=tmcd.samcId
        WHERE 1=1
        <if test="aiopsItemList != null">
            <foreach collection="aiopsItemList" item="item" open="AND samcd.aiopsItem IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        <!--SELECT tmcd.*,
               samc.classCode AS classCode,
               samcd.id AS samcd_id, samcd.samcId AS samcd_samcId,
               samcd.aiopsItemType AS samcd_aiopsItemType, samcd.aiopsItem AS samcd_aiopsItem,
               samcd.holdAuth AS samcd_holdAuth, samcd.isAuthByInstance AS samcd_isAuthByInstance
        FROM tenant_module_contract tmc
        INNER JOIN tenant_module_contract_detail tmcd ON tmc.id=tmcd.tmcId
        INNER JOIN supplier_aiops_module_class samc ON samc.sid=#{sid} AND tmcd.samcId=samc.id
        INNER JOIN supplier_aiops_module_class_detail samcd ON samc.id=samcd.samcId
        <foreach collection="aiopsItemList" item="item" open="AND samcd.aiopsItem IN (" separator=", " close=")">
            #{item}
        </foreach>
        WHERE tmc.sid=#{sid} AND tmc.eid=#{eid} AND TIMESTAMPDIFF(DAY, NOW(), tmc.endDate)>=0-->
    </select>

    <select id="selectTenantModuleContractClassDetailByAiopsItemV2" resultMap="TenantModuleContractMap">
        SELECT tmc.moduleId, sam.moduleCode, sam.moduleName, tmc.endDate, DATEDIFF(endDate,NOW()) days, tmc.status,
               tmcd.id tmcd_id, tmcd.tmcId tmcd_tmcId, tmcd.samcId tmcd_samcId, tmcd.availableCount tmcd_availableCount,
               tmcd.usedCount tmcd_usedCount,
               samc.classCode tmcd_classCode, samc.className tmcd_className,
               samc.className_CN tmcd_className_CN, samc.className_TW tmcd_className_TW,
               EXISTS (SELECT 1 FROM supplier_aiops_module_class_detail samcd
                       WHERE samcd.samcId = samc.id AND holdAuth = 1) AS tmcd_isContainHoldAuth,
               ${marketUrl} as marketUrl, /*雲端市場單一登入url*/
               ifnull(DATEDIFF(tmc.endDate, CURDATE()), 0) as daysUntilExpiration,
               CASE WHEN DATEDIFF(tmc.endDate, CURDATE()) BETWEEN 0 AND 60 THEN true ELSE false END as expiringSoon,/*即将到期*/
               CASE WHEN DATEDIFF(tmc.endDate, CURDATE()) <![CDATA[<]]> 0 THEN true ELSE false END as expired, /*己到期*/
               sam.enableRenewal, /*是否開啟續費*/
               ifnull(sam.cloudMarketProductId, '') as cloudMarketProductId  /*雲端市場產品id*/
        FROM tenant_module_contract tmc
        LEFT JOIN supplier_aiops_module_class samc ON tmc.moduleId = samc.samId
        LEFT JOIN tenant_module_contract_detail tmcd ON tmc.id = tmcd.tmcId AND tmcd.samcId = samc.id
        INNER JOIN supplier_aiops_module sam ON samc.samId = sam.id
        <if test="aiopsItemList != null">
            <foreach collection="aiopsItemList" item="item" open="AND sam.moduleCode IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        WHERE tmc.sid=#{sid} AND tmc.eid=#{eid} AND tmcd.availableCount>0
    </select>
    <update id="updateTenantModuleContractDetailUsedCountByTmcdId">
        UPDATE tenant_module_contract_detail
        SET usedCount=#{usedCount}
        WHERE id = #{tmcdId}
        <if test="isLimited != null and isLimited">
            AND (availableCount >= #{usedCount} OR usedCount > #{usedCount})
        </if>
    </update>

    <update id="subTenantModuleContractDetailUsedCountByTmcdId">
        UPDATE tenant_module_contract_detail
        SET usedCount=usedCount-1
        WHERE id = #{tmcdId}
        AND usedCount > 0  <!-- 防止负数 -->
    </update>

    <select id="selectTenantValidSamcdIdList" resultType="java.lang.Long">
        SELECT DISTINCT samcd.id
        FROM supplier_aiops_module_class samc
        INNER JOIN supplier_aiops_module_class_detail samcd ON samc.id=samcd.samcId
        <if test="aiopsItemList != null">
            <foreach collection="aiopsItemList" item="item" open=" AND samcd.aiopsItem IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        LEFT JOIN (
            SELECT tmcd.samcId
            FROM tenant_module_contract tmc
            INNER JOIN tenant_module_contract_detail tmcd ON tmc.id=tmcd.tmcId
            WHERE tmc.eid = #{eid} AND TIMESTAMPDIFF(DAY, NOW(), tmc.endDate) >= 0
        ) tmcd ON tmcd.samcId = samc.id
        WHERE samc.sid = #{sid} AND (samcd.holdAuth = 0 OR tmcd.samcId IS NOT NULL)
    </select>

    <select id="selectTmcdIdListByTmcIdList" resultType="java.lang.Long">
        SELECT id
        FROM tenant_module_contract_detail
        WHERE 1 != 1
        <if test="tmcIdList != null">
            <foreach collection="tmcIdList" item="item" open=" OR tmcId IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectExactExpireTmcdIdList" resultType="java.lang.Long">
        SELECT tmcd.id
        FROM tenant_module_contract_detail tmcd
        INNER JOIN tenant_module_contract tmc ON tmcd.tmcId = tmc.id
            AND TIMESTAMPDIFF(DAY, DATE_FORMAT(NOW(),'%Y-%m-%d'), DATE_FORMAT(tmc.endDate,'%Y-%m-%d')) <![CDATA[<]]> 0
        WHERE 1 != 1
        <if test="tmcdIdList != null">
            <foreach collection="tmcdIdList" item="item" open=" OR tmcd.id IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectTmcdMapListByEid" resultType="java.util.Map">
        SELECT tmcd.id AS tmcdId,
               EXISTS (SELECT 1 FROM supplier_aiops_module_class_detail samcd
                       WHERE samcd.samcId = tmcd.samcId AND holdAuth = 1) AS isContainHoldAuth
        FROM tenant_module_contract_detail tmcd
        INNER JOIN tenant_module_contract tmc ON tmc.sid = #{sid} AND tmc.eid = #{eid} AND tmcd.tmcId = tmc.id
        <if test="aiopsItemList != null and aiopsItemList.size() > 0">
            INNER JOIN supplier_aiops_module_class samc ON tmcd.samcId = samc.id
            INNER JOIN supplier_aiops_module_class_detail samcd ON samc.id = samcd.samcId
            <foreach collection="aiopsItemList" item="item"
                     open="AND samcd.aiopsItem IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getTenantContractModules" resultMap="TenantModuleContractMap">
        SELECT sam.id moduleId, sam.moduleCode, sam.moduleName,tmc.*,
               tmcd.id AS tmcd_id, tmcd.tmcId AS tmcd_tmcId, tmcd.samcId AS tmcd_samcId,
               CASE WHEN samc.id IS NULL THEN NULL
                    ELSE IFNULL(tmcd.availableCount, 0)
                   END AS tmcd_availableCount,
               CASE WHEN samc.id IS NULL THEN NULL
                    ELSE IFNULL(tmcd.usedCount, 0)
                   END AS tmcd_usedCount,
               samc.classCode AS tmcd_classCode, samc.className AS tmcd_className,
               samc.className_CN AS tmcd_className_CN, samc.className_TW AS tmcd_className_TW
        FROM supplier_aiops_module_class samc
                 LEFT JOIN tenant_module_contract tmc ON tmc.moduleId = samc.samId and tmc.sid=#{sid} and tmc.eid=#{eid}
                 LEFT JOIN supplier_aiops_module_class_detail samcd ON  samcd.samcId = samc.id
                 LEFT JOIN tenant_module_contract_detail tmcd ON samc.id = tmcd.samcId AND tmc.id = tmcd.tmcId
                 LEFT JOIN supplier_aiops_module sam ON samc.samId=sam.id
        order by samc.id
    </select>

    <select id="selectAuthTmc" resultType="String">
        SELECT sam.moduleCode
        FROM tenant_module_contract tmc
                 LEFT JOIN supplier_aiops_module sam ON tmc.moduleId=sam.id
        where eid = #{eid}
        <if test="statusList != null and statusList.size() > 0">
            and tmc.status in
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
    </select>

    <select id="getTenantHoldAuthModules" resultMap="TenantModuleContractMap">
        SELECT sam.id moduleId, sam.moduleCode, sam.moduleName,tmc.*,
               tmcd.id AS tmcd_id, tmcd.tmcId AS tmcd_tmcId, tmcd.samcId AS tmcd_samcId,
               CASE WHEN samc.id IS NULL THEN NULL
                    ELSE IFNULL(tmcd.availableCount, 0)
                   END AS tmcd_availableCount,
               CASE WHEN samc.id IS NULL THEN NULL
                    ELSE IFNULL(tmcd.usedCount, 0)
                   END AS tmcd_usedCount,
               samc.classCode AS tmcd_classCode, samc.className AS tmcd_className,
               samc.className_CN AS tmcd_className_CN, samc.className_TW AS tmcd_className_TW,
               ${marketUrl} as marketUrl, /*雲端市場單一登入url*/
               ifnull(DATEDIFF(tmc.endDate, CURDATE()), 0) as daysUntilExpiration,
               CASE WHEN DATEDIFF(tmc.endDate, CURDATE()) BETWEEN 0 AND 60 THEN true ELSE false END as expiringSoon, /*即将到期*/
               CASE WHEN DATEDIFF(tmc.endDate, CURDATE()) <![CDATA[<]]> 0 THEN true ELSE false END as expired, /*己到期*/
               sam.enableRenewal, /*是否開啟續費*/
               ifnull(sam.cloudMarketProductId, '') as cloudMarketProductId  /*雲端市場產品id*/
        FROM supplier_aiops_module_class samc
        LEFT JOIN tenant_module_contract tmc ON tmc.moduleId = samc.samId and tmc.sid=#{sid} and tmc.eid=#{eid}
        LEFT JOIN supplier_aiops_module_class_detail samcd ON  samcd.samcId = samc.id
        LEFT JOIN tenant_module_contract_detail tmcd ON samc.id = tmcd.samcId AND tmc.id = tmcd.tmcId
        LEFT JOIN supplier_aiops_module sam ON samc.samId=sam.id
        WHERE samcd.holdAuth = 1
        order by samc.id
    </select>

    <select id="getTenantHoldAuthExpirationStatus" resultMap="TenantModuleContractMap">
        select * from (
              SELECT sam.moduleCode, sam.moduleName,tmc.*,
                     tmcd.id AS tmcd_id, tmcd.tmcId AS tmcd_tmcId, tmcd.samcId AS tmcd_samcId,
                     CASE WHEN samc.id IS NULL THEN NULL
                          ELSE IFNULL(tmcd.availableCount, 0)
                         END AS tmcd_availableCount,
                     CASE WHEN samc.id IS NULL THEN NULL
                          ELSE IFNULL(tmcd.usedCount, 0)
                         END AS tmcd_usedCount,
                     samc.classCode AS tmcd_classCode, samc.className AS tmcd_className,
                     samc.className_CN AS tmcd_className_CN, samc.className_TW AS tmcd_className_TW,
                     ${marketUrl} as marketUrl, /*雲端市場單一登入url*/
                     ifnull(DATEDIFF(tmc.endDate, CURDATE()), 0) as daysUntilExpiration,
                     CASE WHEN DATEDIFF(tmc.endDate, CURDATE()) BETWEEN 0 AND 60 THEN true ELSE false END as expiringSoon, /*即将到期*/
                     CASE WHEN DATEDIFF(tmc.endDate, CURDATE()) <![CDATA[<]]> 0 THEN true ELSE false END as expired, /*己到期*/
                     sam.enableRenewal, /*是否開啟續費*/
                     ifnull(sam.cloudMarketProductId, '') as cloudMarketProductId  /*雲端市場產品id*/
              FROM supplier_aiops_module_class samc
                       LEFT JOIN tenant_module_contract tmc ON tmc.moduleId = samc.samId
                       LEFT JOIN supplier_aiops_module_class_detail samcd ON  samcd.samcId = samc.id
                       LEFT JOIN tenant_module_contract_detail tmcd ON samc.id = tmcd.samcId AND tmc.id = tmcd.tmcId
                       LEFT JOIN supplier_aiops_module sam ON samc.samId=sam.id
              WHERE samcd.holdAuth = 1
              order by samc.id
        ) a where (expired || expiringSoon)
        <if test="eid != null and eid != ''">
            AND eid = #{eid}
        </if>
    </select>

    <select id="getModulesByCondition" resultMap="TenantModuleContractMap">
        SELECT sam.id moduleId, sam.moduleCode, sam.moduleName,tmc.*,
               tmcd.id AS tmcd_id, tmcd.tmcId AS tmcd_tmcId, tmcd.samcId AS tmcd_samcId,
               CASE WHEN samc.id IS NULL THEN NULL
                    ELSE IFNULL(tmcd.availableCount, 0)
                   END AS tmcd_availableCount,
               CASE WHEN samc.id IS NULL THEN NULL
                    ELSE IFNULL(tmcd.usedCount, 0)
                   END AS tmcd_usedCount,
                CASE WHEN IFNULL(tmc.endDate,'')='' THEN 1
                WHEN TIMESTAMPDIFF(DAY, NOW(), tmc.endDate)>=0 THEN 0
                ELSE 1 END AS expired,
               samc.classCode AS tmcd_classCode, samc.className AS tmcd_className,
               samc.className_CN AS tmcd_className_CN, samc.className_TW AS tmcd_className_TW
        FROM supplier_aiops_module_class samc
        INNER JOIN tenant_module_contract tmc ON tmc.moduleId = samc.samId and tmc.sid=#{sid} and tmc.eid=#{eid}
        LEFT JOIN supplier_aiops_module_class_detail samcd ON  samcd.samcId = samc.id
        LEFT JOIN tenant_module_contract_detail tmcd ON samc.id = tmcd.samcId AND tmc.id = tmcd.tmcId
        LEFT JOIN supplier_aiops_module sam ON samc.samId=sam.id
        WHERE samcd.holdAuth = 1
        <if test="expired != null ">
            <choose>
                <when test="expired == false">
                    AND TIMESTAMPDIFF(DAY, NOW(), tmc.endDate)>=0
                </when>
                <otherwise>
                    AND TIMESTAMPDIFF(DAY, NOW(), tmc.endDate)<![CDATA[<]]>0
                </otherwise>
            </choose>
        </if>
        <if test="moduleName != null and moduleName != ''">
            AND sam.moduleName like CONCAT('%',#{moduleName},'%')
        </if>
        order by tmc.endDate desc,samc.id
    </select>

    <select id="getServiceTenantHoldAuthModules" resultMap="TenantModuleContractMap">
        SELECT sam.id moduleId, sam.moduleCode, sam.moduleName,tmc.*,
               tmcd.id AS tmcd_id, tmcd.tmcId AS tmcd_tmcId, tmcd.samcId AS tmcd_samcId,
               CASE WHEN samc.id IS NULL THEN NULL
                    ELSE IFNULL(tmcd.availableCount, 0)
                   END AS tmcd_availableCount,
               CASE WHEN samc.id IS NULL THEN NULL
                    ELSE IFNULL(tmcd.usedCount, 0)
                   END AS tmcd_usedCount,
               samc.classCode AS tmcd_classCode, samc.className AS tmcd_className,
               samc.className_CN AS tmcd_className_CN, samc.className_TW AS tmcd_className_TW
        FROM supplier_aiops_module_class samc
                 LEFT JOIN tenant_module_contract tmc ON tmc.moduleId = samc.samId
                 LEFT JOIN tenant_module_contract_detail tmcd ON samc.id = tmcd.samcId AND tmc.id = tmcd.tmcId
                 LEFT JOIN supplier_aiops_module sam ON samc.samId=sam.id
        WHERE tmc.eid=#{eid}
        order by samc.id
    </select>

    <select id="getTenantIdByServiceCode" resultType="java.lang.String">
        SELECT t.id
        FROM supplier_tenant_map stm
        left join tenant t on stm.eid=t.sid
        WHERE stm.sid=#{sid} and stm.serviceCode=#{serviceCode}
        limit 1
    </select>

    <select id="getTenantModuleContractsByServiceCode" resultMap="TenantModuleContractMap">
        SELECT
            tmc.id AS id, tmc.sid AS sid, tmc.eid AS eid, tmc.moduleId AS moduleId,
            tmc.status AS status, tmc.startDate AS startDate, tmc.endDate AS endDate,
            tmc.staffId AS staffId, se.name AS staffName, tmc.openId AS openId,
            tmc.userName AS userName, tmc.noticeEmail AS noticeEmail, tmc.itemNo AS itemNo,
            tmc.serverCount AS serverCount, tmc.workStationCount AS workStationCount,
            tmc.iotCount AS iotCount,
            CASE WHEN IFNULL(tmc.endDate,'')='' THEN NULL
            WHEN TIMESTAMPDIFF(DAY, NOW(), tmc.endDate)>=0 THEN 0
            ELSE 1 END AS expired,
            sam.moduleCode AS moduleCode, sam.moduleName AS moduleName,
            tmc.tmcd_id, tmc.tmcd_tmcId, tmc.tmcd_samcId,
            tmc.tmcd_availableCount, tmc.tmcd_usedCount,
            tmc.tmcd_classCode, tmc.tmcd_className,
            tmc.tmcd_className_CN, tmc.tmcd_className_TW

        FROM (
            SELECT DISTINCT t.sid, t.id
            FROM tenant t
            LEFT JOIN supplier_tenant_map stm ON t.sid=stm.eid
            WHERE 1=1 AND stm.sid=#{sid}

            <if test="serviceCode != null and serviceCode != ''">
                AND (t.id LIKE "%"#{serviceCode}"%" )
            </if>
            <if test="eid != null and eid != ''">
                AND stm.eid = #{eid}
            </if>
        ) main
        INNER JOIN (
            SELECT tmc.*,
                tmcd.id AS tmcd_id, tmcd.tmcId AS tmcd_tmcId, tmcd.samcId AS tmcd_samcId,
                CASE WHEN samc.id IS NULL THEN NULL
                ELSE IFNULL(tmcd.availableCount, 0)
                END AS tmcd_availableCount,
                CASE WHEN samc.id IS NULL THEN NULL
                ELSE IFNULL(tmcd.usedCount, 0)
                END AS tmcd_usedCount,
                samc.classCode AS tmcd_classCode, samc.className AS tmcd_className,
                samc.className_CN AS tmcd_className_CN, samc.className_TW AS tmcd_className_TW
            FROM tenant_module_contract tmc
            LEFT JOIN supplier_aiops_module_class samc ON tmc.moduleId = samc.samId
            LEFT JOIN tenant_module_contract_detail tmcd ON samc.id = tmcd.samcId AND tmc.id = tmcd.tmcId
            WHERE tmc.sid=#{sid}
        ) tmc ON main.sid=tmc.eid
        <if test="moduleId != null and moduleId != ''">
            AND tmc.moduleId=#{moduleId}
        </if>
        LEFT JOIN supplier_aiops_module sam ON tmc.moduleId=sam.id
        LEFT JOIN supplier_employee se ON tmc.sid = se.sid AND tmc.staffId=se.userSid

        WHERE 1=1
        ORDER BY main.id ASC,tmc.updateTime DESC
    </select>

    <select id="getProductName" resultType="java.lang.String">
        SELECT sp.productCategory
        FROM supplier_product sp
        WHERE sp.sid=#{sid} and sp.productCode=#{productCode}
        limit 1
    </select>

    <insert id="saveCustomerService">
        insert into `${escloudDBName}`.mars_customerservice(CustomerServiceCode,ProductCode,ProductCategory,ContractState,ContractStartDate,ContractExprityDate,IsTrial,contractSource)
        values(#{customerServiceCode}, #{productCode}, #{productCategory}, #{contractState}, #{contractStartDate},#{contractExprityDate},#{isTrial},#{contractSource})
        ON DUPLICATE KEY UPDATE ContractState=#{contractState} ,ContractStartDate=#{contractStartDate} ,ContractExprityDate=#{contractExprityDate},IsTrial=#{isTrial}
    </insert>

    <select id="getTenantContractById" resultType="com.digiwin.escloud.aiouser.model.tenant.TenantModuleContract">
        SELECT id,sid,eid,moduleId,status,orderMethod,startDate,endDate,staffId,openId,userName,noticeEmail,itemNo
        FROM tenant_module_contract
        WHERE id=#{id}
        limit 1
    </select>

    <select id="selectTmcdHoldAuthRelateByTmcdIdList" resultType="java.util.Map">
        SELECT tmcd.id AS tmcdId,
               EXISTS (SELECT 1 FROM supplier_aiops_module_class_detail samcd
                       WHERE samcd.samcId = tmcd.samcId AND holdAuth = 1) AS isContainHoldAuth
        FROM tenant_module_contract_detail tmcd
        WHERE 1 != 1
        <foreach collection="tmcdIdList" item="item" open=" OR tmcd.id IN (" separator=", " close=")">
            #{item}
        </foreach>
    </select>
    <select id="getSupplierFlag" resultType="java.lang.String">
        select flag from supplier a where a.sid = #{sid}
    </select>

    <select id="selectTmcListByEidSamcIdList" resultMap="TenantModuleContractMap">
        SELECT DISTINCT tmc.id, tmc.sid, tmc.eid, tmc.moduleId, tmc.status, tmc.orderMethod, tmc.startDate, tmc.endDate,
                        tmc.staffId, tmc.openId, tmc.userName, tmc.noticeEmail, tmc.itemNo,
                        tmc.id AS tmcd_tmcId, samc.id AS tmcd_samcId, 0 AS tmcd_availableCount, 0 AS tmcd_usedCount,
                        samc.classCode AS tmcd_classCode, samc.className AS tmcd_className,
                        samcd.aiopsItemType AS tmcd_samcd_aiopsItemType, samcd.aiopsItem AS tmcd_samcd_aiopsItem
        FROM supplier_aiops_module_class samc
        LEFT JOIN supplier_aiops_module_class_detail samcd ON samc.id = samcd.samcId
        LEFT JOIN tenant_module_contract tmc ON samc.samId = tmc.moduleId AND tmc.eid = #{eid}
        <where>
            <if test="samcIdList != null">
                <foreach collection="samcIdList" item="item" open=" AND samc.id IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectTmcdListByTmcIdList" resultMap="TenantModuleContractDetailMap">
        SELECT id,availableCount,samcId,usedCount
        FROM tenant_module_contract_detail
        WHERE 1 != 1
        <if test="tmcIdList != null">
            <foreach collection="tmcIdList" item="item" open=" OR tmcId IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectAsiaTenantByEid" resultType="com.digiwin.escloud.aiouser.model.tenant.TenantTp">
        SELECT *
        FROM tenant_tp
        WHERE sid = #{sid} AND eid = #{eid} AND tpCode = 'ASIA_INFO'
    </select>
    <select id="selectAllAsiaTenant" parameterType="com.digiwin.escloud.common.model.TenantTpParams" resultType="com.digiwin.escloud.aiouser.model.tenant.TenantTp">
        SELECT tt.*,t.name,t.customerFullNameCH,t.customerFullNameEN,t.customer_id as serviceCode
        FROM tenant_tp  tt
        INNER JOIN tenant t on t.sid = tt.eid
        WHERE tt.sid = #{sid}
        <if test="eidList != null and eidList.size() > 0">
            <foreach collection="eidList" item="item" open=" AND tt.eid IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        <if test="tpCode != null and tpCode !=''">
           AND tt.tpCode = #{tpCode}
        </if>
        <if test="eid != null">
            AND eid = #{eid}
        </if>
    </select>
    <insert id="insertOrUpdateTenantTp" parameterType="com.digiwin.escloud.aiouser.model.tenant.TenantTp">
        INSERT INTO tenant_tp (
            id,
            eid,
            sid,
            tpTenantId,
            tpCode,
            tpTenantName,
            tpAlias,
            tpEmail,
            tpDescription,
            tpProgram,
            tpAgentDownloadUrl
        ) VALUES (
                     #{id,jdbcType=BIGINT},
                     #{eid,jdbcType=BIGINT},
                     #{sid,jdbcType=BIGINT},
                     #{tpTenantId,jdbcType=VARCHAR},
                     #{tpCode},
                     #{tpTenantName,jdbcType=VARCHAR},
                     #{tpAlias,jdbcType=VARCHAR},
                     #{tpEmail,jdbcType=VARCHAR},
                     #{tpDescription,jdbcType=VARCHAR},
                     #{tpProgram,jdbcType=VARCHAR},
                     #{tpAgentDownloadUrl,jdbcType=VARCHAR}
                 )
        ON DUPLICATE KEY UPDATE
                             eid = VALUES(eid),
                             sid = VALUES(sid),
                             tpTenantId = VALUES(tpTenantId),
                             tpCode = VALUES(tpCode),
                             tpTenantName = VALUES(tpTenantName),
                             tpAlias = VALUES(tpAlias),
                             tpEmail = VALUES(tpEmail),
                             tpDescription = VALUES(tpDescription),
                             tpAgentDownloadUrl = VALUES(tpAgentDownloadUrl)
    </insert>
    <insert id="insertTenantTp" parameterType="com.digiwin.escloud.aiouser.model.tenant.TenantTp">
        INSERT INTO tenant_tp (
            id,
            eid,
            sid,
            tpTenantId,
            tpCode,
            tpTenantName,
            tpAlias,
            tpEmail,
            tpDescription,
            tpProgram,
            tpAgentDownloadUrl
        ) VALUES (
                     #{id,jdbcType=BIGINT},
                     #{eid,jdbcType=BIGINT},
                     #{sid,jdbcType=BIGINT},
                     #{tpTenantId,jdbcType=VARCHAR},
                     #{tpCode},
                     #{tpTenantName,jdbcType=VARCHAR},
                     #{tpAlias,jdbcType=VARCHAR},
                     #{tpEmail,jdbcType=VARCHAR},
                     #{tpDescription,jdbcType=VARCHAR},
                     #{tpProgram,jdbcType=VARCHAR},
                     #{tpAgentDownloadUrl,jdbcType=VARCHAR}
                 )
    </insert>

    <update id="updateTenantTp" parameterType="com.digiwin.escloud.aiouser.model.tenant.TenantTp">
        UPDATE tenant_tp
        <set>
            <if test="eid != null">
                eid = #{eid},
            </if>
            <if test="sid != null">
                sid = #{sid},
            </if>
            <if test="tpTenantId != null">
                tpTenantId = #{tpTenantId},
            </if>
            <if test="tpCode != null">
                tpCode = #{tpCode},
            </if>
            <if test="tpTenantName != null">
                tpTenantName = #{tpTenantName},
            </if>
            <if test="tpAlias != null">
                tpAlias = #{tpAlias},
            </if>
            <if test="tpEmail != null">
                tpEmail = #{tpEmail},
            </if>
            <if test="tpDescription != null">
                tpDescription = #{tpDescription},
            </if>
            <if test="tpAgentDownloadUrl != null">
                tpAgentDownloadUrl = #{tpAgentDownloadUrl},
            </if>
            <if test="tpProgram != null">
                tpProgram = #{tpProgram},
            </if>

        </set>
        WHERE id = #{id}
    </update>

    <select id="getTenantTpByParams" resultType="com.digiwin.escloud.aiouser.model.tenant.TenantTp">
        SELECT id,eid, sid, tpTenantId, tpCode, tpTenantName, tpAlias, tpEmail, tpDescription, tpAgentDownloadUrl,tpProgram
        FROM tenant_tp
        <where>
            <if test="tpTenantName != null and tpTenantName != ''">
                AND tpTenantName = #{tpTenantName}
            </if>
            <if test="eid != null and eid != ''">
                AND eid = #{eid}
            </if>
        </where>
        Limit 1
    </select>

    <update id="updateTenantIsv">
        UPDATE tenant
        SET isv = #{isv}
        WHERE sid = #{tenantSid}
    </update>

    <select id="selectSupplierAiopsModule"  resultType="com.digiwin.escloud.aiouser.model.supplier.SupplierAiopsModule">
        SELECT *
        FROM supplier_aiops_module

        WHERE sid = #{sid}
    </select>

    <select id="selectTenantModuleContractByISVSid"  resultType="com.digiwin.escloud.aiouser.model.tenant.TenantModuleContract">
        SELECT dp_t_main.* , sam.moduleCode,sam.moduleName
        FROM tenant_module_contract dp_t_main
                 LEFT JOIN supplier_aiops_module sam on dp_t_main.moduleId = sam.id
                 LEFT JOIN supplier_tenant_map stm ON dp_t_main.eid = stm.eid
        WHERE serviceIsvSid = #{serviceIsvSid} AND stm.sid = #{sid}
        <foreach collection="eidList" item="item" open=" AND dp_t_main.eid IN (" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="getTenantTpList"  resultType="com.digiwin.escloud.aiouser.model.tenant.TenantTp">
        SELECT * FROM tenant_tp
    </select>

    <select id="selectTenantIsNeedLogin" resultType="Boolean">
        select needLogin from tenant_setting where eid = #{eid}
    </select>

    <insert id="insertOrUpdateKitLogin">
        INSERT INTO tenant_setting (
            id, eid, needLogin
        ) VALUES (
                     #{id}, #{eid}, #{needLogin}
                 )
        ON DUPLICATE KEY UPDATE
                             needLogin = VALUES(needLogin)
    </insert>

    <update id="updateTenantModuleContractHistories" parameterType="java.util.List">
        UPDATE tenant_module_contract_history
        SET operateType = #{status}
        WHERE id = #{id}
    </update>

    <select id="getSubscribeTenantList" resultMap="SubscribeTenantMap">
        SELECT main.*,
        stm.id AS st_id, stm.sid AS st_sid, stm.eid AS st_eid, stm.serviceCode AS st_serviceCode,uta.attention, uta.receiveWarningMail,
        tc.id as tc_id,tc.eid as tc_eid,tc.sid as tc_sid,tc.productCode as tc_productCode,tc.productShortName as tc_productShortName,
        tc.contractState as tc_contractState,tc.contractStartDate as tc_contractStartDate,
        tc.contractExpiryDate as tc_contractExpiryDate,tc.updateTime as tc_updateTime
        FROM (
        SELECT DISTINCT dp_t_main.sid, dp_t_main.id, dp_t_main.name, dp_t_main.customer_id, dp_t_main.taxCode, dp_t_main.status, dp_t_main.registerPhone,
        dp_t_main.address, dp_t_main.contacts, dp_t_main.email, dp_t_main.phone, dp_t_main.cellphone_prefix, dp_t_main.telephone, dp_t_main.installed
        FROM tenant dp_t_main
        LEFT JOIN tenant_module_contract tmc ON dp_t_main.sid=tmc.eid
        LEFT JOIN supplier_aiops_module sam ON tmc.moduleId=sam.id
        LEFT JOIN tenant_contract tc ON dp_t_main.sid=tc.eid
        LEFT JOIN supplier_tenant_map stm ON dp_t_main.sid=stm.eid
        LEFT JOIN user_tenant_attention uta ON dp_t_main.sid=uta.eid AND uta.userSid=#{userSid}
        WHERE 1=1 AND stm.sid=#{sid}
        <if test="content != null and content != ''">
            AND (dp_t_main.id LIKE "%"#{content}"%" OR dp_t_main.name LIKE "%"#{content}"%" OR stm.serviceCode LIKE "%"#{content}"%")
        </if>
        <choose>
            <when test="attention != null and attention">
                AND EXISTS(SELECT 1 FROM user_tenant_attention
                WHERE sid=#{sid} AND attention=#{attention} AND eid=dp_t_main.sid AND userSid=#{userSid})
            </when>
            <when test="attention != null and !attention">
                AND NOT EXISTS(SELECT 1 FROM user_tenant_attention
                WHERE userSid=#{userSid} AND sid=#{sid} AND attention=1 AND dp_t_main.sid=eid)
            </when>
        </choose>
        <if test="tenantModuleStatusList != null and tenantModuleStatusList.size() > 0">
            <foreach collection="tenantModuleStatusList" item="item" open=" AND tmc.status IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        <if test="moduleCodeList != null and moduleCodeList.size() > 0">
            <foreach collection="moduleCodeList" item="item" open=" AND sam.moduleCode IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        <if test="productCode != null and productCode != ''">
            AND tc.productCode=#{productCode}
        </if>
        <if test="contractUpdateDate != null">
            AND tc.updateTime<![CDATA[>=]]>#{contractUpdateDate}
        </if>
        <if test="installed != null">
            AND dp_t_main.installed=#{installed}
        </if>
        ORDER BY tc.updateTime DESC, dp_t_main.id ASC
        LIMIT #{start}, #{size}
        ) main
        LEFT JOIN tenant_contract tc ON main.sid=tc.eid
        LEFT JOIN supplier_tenant_map stm ON main.sid=stm.eid
        LEFT JOIN user_tenant_attention uta ON main.sid=uta.eid AND uta.userSid=#{userSid}
        WHERE 1=1 AND stm.sid=#{sid}
        <if test="productCode != null and productCode != ''">
            AND tc.productCode=#{productCode}
        </if>
        ORDER BY tc.updateTime DESC, main.id ASC
    </select>

    <select id="getSubscribeTenantCnt" resultType="java.lang.Integer">
        SELECT count(DISTINCT dp_t_main.sid) AS cnt
        FROM tenant dp_t_main
        LEFT JOIN tenant_module_contract tmc ON dp_t_main.sid=tmc.eid
        LEFT JOIN supplier_aiops_module sam ON tmc.moduleId=sam.id
        LEFT JOIN tenant_contract tc ON dp_t_main.sid=tc.eid
        LEFT JOIN supplier_tenant_map stm ON dp_t_main.sid=stm.eid
        LEFT JOIN user_tenant_attention uta ON dp_t_main.sid=uta.eid AND uta.userSid=#{userSid}
        WHERE 1=1 AND stm.sid=#{sid}
        <if test="content != null and content != ''">
            AND (dp_t_main.id LIKE "%"#{content}"%" OR dp_t_main.name LIKE "%"#{content}"%" OR stm.serviceCode LIKE "%"#{content}"%")
        </if>
        <choose>
            <when test="attention != null and attention">
                AND EXISTS(SELECT 1 FROM user_tenant_attention
                WHERE sid=#{sid} AND attention=#{attention} AND eid=dp_t_main.sid AND userSid=#{userSid})
            </when>
            <when test="attention != null and !attention">
                AND NOT EXISTS(SELECT 1 FROM user_tenant_attention
                WHERE userSid=#{userSid} AND sid=#{sid} AND attention=1 AND dp_t_main.sid=eid)
            </when>
        </choose>
        <if test="tenantModuleStatusList != null and tenantModuleStatusList.size() > 0">
            <foreach collection="tenantModuleStatusList" item="item" open=" AND tmc.status IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        <if test="moduleCodeList != null and moduleCodeList.size() > 0">
            <foreach collection="moduleCodeList" item="item" open=" AND sam.moduleCode IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        <if test="productCode != null and productCode != ''">
            AND tc.productCode=#{productCode}
        </if>
        <if test="contractUpdateDate != null">
            AND tc.updateTime<![CDATA[>=]]>#{contractUpdateDate}
        </if>
        <if test="installed != null">
            AND dp_t_main.installed=#{installed}
        </if>
    </select>

    <update id="updateTenantInstalledStatus">
        UPDATE tenant
        SET installed = #{installed}
        WHERE sid = #{tenantSid}
    </update>

    <select id="getTenantModuleContractByModuleStatus" resultMap="TenantModuleContractMap">
        SELECT tmc.*,
        CASE WHEN IFNULL(tmc.endDate,'')='' THEN NULL
        WHEN TIMESTAMPDIFF(DAY, NOW(), tmc.endDate)>=0 THEN 0
        ELSE 1 END AS expired,
        sam.moduleCode AS moduleCode, sam.moduleName AS moduleName,
        tmcd.id AS tmcd_id, tmcd.tmcId AS tmcd_tmcId, tmcd.samcId AS tmcd_samcId,
        CASE WHEN samc.id IS NULL THEN NULL
        ELSE IFNULL(tmcd.availableCount, 0)
        END AS tmcd_availableCount,
        CASE WHEN samc.id IS NULL THEN NULL
        ELSE IFNULL(tmcd.usedCount, 0)
        END AS tmcd_usedCount,
        EXISTS (SELECT 1 FROM supplier_aiops_module_class_detail samcd
        WHERE samcd.samcId = samc.id AND holdAuth = 1) AS tmcd_isContainHoldAuth,
        samc.classCode AS tmcd_classCode, samc.className AS tmcd_className,
        samc.className_CN AS tmcd_className_CN, samc.className_TW AS tmcd_className_TW
        FROM tenant_module_contract tmc
        LEFT JOIN tenant_module_contract_detail tmcd ON tmc.id=tmcd.tmcId
        LEFT JOIN supplier_aiops_module_class samc ON tmcd.samcId=samc.id
        LEFT JOIN supplier_aiops_module sam ON tmc.moduleId=sam.id
        WHERE tmc.sid=#{sid}
        <if test="eidList != null and eidList.size() > 0">
            <foreach collection="eidList" item="item" open=" AND tmc.eid IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        <if test="moduleStatusList != null and moduleStatusList.size() > 0">
            <foreach collection="moduleStatusList" item="item" open=" AND tmc.status IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getTenantModuleContractCntByModuleStatus" resultType="java.util.Map">
        SELECT eid,count(*) AS cnt
        FROM tenant_module_contract tmc
        WHERE tmc.sid=#{sid}
        <if test="eidList != null and eidList.size() > 0">
            <foreach collection="eidList" item="item" open=" AND tmc.eid IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        <if test="moduleStatusList != null and moduleStatusList.size() > 0">
            <foreach collection="moduleStatusList" item="item" open=" AND tmc.status IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY eid
    </select>

    <select id="getCustomerServiceInfo" resultType="com.digiwin.escloud.aiouser.model.customer.CustomerServiceInfo">
        select a.CustomerServiceCode as customerServiceCode, a.ProductCode as productCode,a.cust_level as custLevel,
               a.IsTrial as isTrial,a.ContractStartDate as contractStartDate,a.ContractExprityDate as contractExprityDate,
               a.HasOwnerService as hasOwnerService,a.HasTextService as hasTextService,a.HasOwnerIssueService as hasOwnerIssueService
        from mars_customerservice a
        where a.CustomerServiceCode=#{serviceCode} and a.ProductCode =#{productCode}
        limit 1;
    </select>
</mapper>