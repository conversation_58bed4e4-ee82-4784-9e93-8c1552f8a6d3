<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aioitms.networksecurity.dao.NetworkSecurityExamMapper">

    <resultMap id="AiopsExamItemInstanceScoreMap" type="com.digiwin.escloud.aioitms.exam.model.AiopsExamItemInstanceScore">
        <id column="id" property="id" />
        <result column="aerId" property="aerId" />
        <result column="aiopsItem" property="aiopsItem" />
        <result column="aiopsItemId" property="aiopsItemId" />
        <result column="examComplete" property="examComplete" />
        <result column="examInstanceStatus" property="examInstanceStatus" />
        <result column="scoreTime" property="scoreTime" />
        <result column="levelCode" property="levelCode" />
        <result column="createDate" property="createDate" />
        <result column="updateDate" property="updateDate" />
        <result column="aiopsItemCount" property="aiopsItemCount" />
        <result column="aeimId" property="aeimId" />
        <result column="networkExamCategoryCode" property="networkExamCategoryCode" />
        <result column="modelCode" property="modelCode" />
        <result column="categoryCode" property="categoryCode" />
        <result column="networkExamAssetId" property="networkExamAssetId" />
        <result column="deviceName" property="deviceName" />
        <result column="aiopsItemTotal" property="aiopsItemTotal" />
        <collection property="aiList" ofType="com.digiwin.escloud.aioitms.instance.model.AiopsItem"
                    columnPrefix="ai_"
                    resultMap="AiopsItemMap"/>
        <collection property="nseptList" ofType="com.digiwin.escloud.aioitms.networksecurity.model.NetworkSecurityExaminationProjectType"
                    columnPrefix="nsept_"
                    resultMap="NetworkSecurityExaminationProjectTypeMap"/>
    </resultMap>

    <resultMap id="AiopsItemMap" type="com.digiwin.escloud.aioitms.instance.model.AiopsItem">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="name_CN" property="name_CN" />
        <result column="name_TW" property="name_TW" />
        <result column="usedCount" property="usedCount" />
        <result column="availableCount" property="availableCount" />
    </resultMap>

    <resultMap id="NetworkSecurityExaminationProjectTypeMap" type="com.digiwin.escloud.aioitms.networksecurity.model.NetworkSecurityExaminationProjectType">
        <id column="id" property="id" />
        <result column="parentCode" property="parentCode" />
        <result column="categoryName" property="categoryName" />
        <result column="categoryName_TW" property="categoryName_TW" />
        <result column="categoryName_CN" property="categoryName_CN" />
        <result column="categoryName_EN" property="categoryName_EN" />
        <result column="categoryCode" property="categoryCode" />
        <result column="modelCode" property="modelCode" />
        <result column="modelName" property="modelName" />
        <result column="createTime" property="createTime" />
        <result column="updateTime" property="updateTime" />
        <result column="dataCount" property="dataCount" />
        <result column="modelPk" property="modelPk" />
        <result column="modelPkValue" property="modelPkValue" />
    </resultMap>

    <select id="selectExamIdByCode" resultType="Long">
        SELECT id FROM aiops_exam
        WHERE code = #{code}
        LIMIT 1
    </select>

    <!-- 分页查询exam_record -->
     <select id="selectExamRecordsByExamId" resultType="com.digiwin.escloud.aioitms.exam.model.AiopsExamRecord">
        SELECT * FROM aiops_exam_record
        WHERE aeId IN
        <foreach item="aeId" collection="aeIdList" open="(" separator="," close=")">
            #{aeId}
        </foreach>
        <if test="userName != null and userName != ''">
            AND userName LIKE CONCAT('%', #{userName}, '%')
        </if>
         <if test="eid != null">
             AND eid = #{eid}
         </if>
        <if test="examStartTimeStart != null and examStartTimeStart != ''">
            AND examStartTime &gt;= #{examStartTimeStart}
        </if>
        <if test="examStartTimeEnd != null and examStartTimeEnd != ''">
            AND examStartTime &lt;= #{examStartTimeEnd}
        </if>
        <if test="examTitle != null and examTitle != ''">
            AND examTitle LIKE CONCAT('%', #{examTitle}, '%')
        </if>
        ORDER BY createDate DESC
    </select>


    <resultMap id="BaseResultMap" type="com.digiwin.escloud.aioitms.exam.model.AiopsExamRecordsReportRecord">
        <id column="id" property="id" />
        <result column="sid" property="sid" />
        <result column="eid" property="eid" />
        <result column="aerId" property="aerId" />
        <result column="serviceCode" property="serviceCode" />
        <result column="customerName" property="customerName" />
        <result column="customerFullName" property="customerFullName" />
        <result column="reportStatus" property="reportStatus" />
        <result column="userSid" property="userSid" />
        <result column="userName" property="userName" />
        <result column="reportDate" property="reportDate" />
        <result column="generationTime" property="generationTime" />
        <result column="createTime" property="createTime" />
        <result column="updateTime" property="updateTime" />
    </resultMap>

    <select id="selectReportByAerIds" resultMap="BaseResultMap">
        SELECT *
        FROM aiops_exam_records_report_records
        WHERE aerId IN
        <foreach item="aerId" collection="aerIdList" open="(" separator="," close=")">
            #{aerId}
        </foreach>
        ORDER BY createTime DESC
    </select>


    <insert id="insertOrUpdateProjectType" parameterType="com.digiwin.escloud.aioitms.networksecurity.model.NetworkSecurityExaminationProjectType">
        INSERT INTO network_security_examination_project_type (id, parentCode, categoryName, categoryCode, modelCode, modelName, createTime, updateTime)
        VALUES (#{id}, #{parentCode}, #{categoryName}, #{categoryCode}, #{modelCode}, #{modelName}, #{createTime}, #{updateTime})
        ON DUPLICATE KEY UPDATE
                             parentCode = #{parentCode},
                             categoryName = #{categoryName},
                             categoryCode = #{categoryCode},
                             modelCode = #{modelCode},
                             modelName = #{modelName},
                             updateTime = #{updateTime}
    </insert>

    <select id="selectProjectType" resultType="com.digiwin.escloud.aioitms.networksecurity.model.NetworkSecurityExaminationProjectType">
        SELECT * FROM network_security_examination_project_type
        WHERE 1=1
        <if test="categoryCode != null and categoryCode != ''">
            AND categoryCode = #{categoryCode}
        </if>
        <if test="categoryName != null and categoryName != ''">
            AND categoryName LIKE CONCAT('%', #{categoryName}, '%')
        </if>
        <if test="parentCode != null and parentCode != ''">
            AND parentCode = #{parentCode}
        </if>
        <if test="filterNullModel != null and filterNullModel">
            AND modelCode IS NOT NULL AND modelCode != ''
        </if>
        <if test="modelCode != null and modelCode != ''">
            AND modelCode = #{modelCode}
        </if>
    </select>

    <delete id="deleteByAiopsItemIdAndAerId">
        DELETE FROM aiops_exam_item_instance_score
        WHERE aiopsItemId = #{aiopsItemId} AND aerId = #{aerId}
    </delete>

    <select id="selectExamItemInstanceScore"
            resultType="com.digiwin.escloud.aioitms.exam.model.AiopsExamItemInstanceScore">
        SELECT id, aerId, aiopsItem, aiopsItemId, examComplete, examScore, scoreTime, levelCode,
               examInstanceStatus, networkExamCategoryCode, networkExamAssetId, createDate, updateDate, deviceName
        FROM aiops_exam_item_instance_score
        WHERE aerId = #{aerId}
    </select>

    <select id="selectExamIdByCodes" resultType="com.digiwin.escloud.aioitms.exam.model.AiopsExam">
        SELECT code, id
        FROM aiops_exam
        WHERE code IN
        <foreach collection="codes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

    <select id="selectExamItemInstanceScoreTotal" resultMap="AiopsExamItemInstanceScoreMap">
        SELECT aeiis.networkExamCategoryCode,
               aeiis.aiopsItem,
               count(aeiis.aiopsItem) AS aiopsItemTotal,
               ai.name_CN             AS ai_name_CN,
               ai.name_TW             AS ai_name_TW,
               nsept.categoryName_CN  AS nsept_categoryName_CN,
               nsept.categoryName_TW  AS nsept_categoryName_TW
        FROM aiops_exam_item_instance_score aeiis
                 LEFT JOIN aiops_item ai ON ai.`code` = aeiis.aiopsItem
                 LEFT JOIN network_security_examination_project_type nsept
                           ON nsept.categoryCode = aeiis.networkExamCategoryCode
        WHERE aerId = #{aerId}
        GROUP BY aiopsItem
    </select>

    <select id="selectExamRecord"
            resultType="com.digiwin.escloud.aioitms.exam.model.AiopsExamRecord">
        SELECT aer.*, ae.code
        FROM aiops_exam_record aer
        LEFT JOIN aiops_exam ae ON ae.id = aer.aeId
        WHERE aer.eid = #{eid}
        AND ae.code IN
        <foreach collection="codes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        AND aer.createDate >= DATE_SUB(NOW(), INTERVAL 3 MONTH)
        AND aer.examStatus = 'EXAM_COMPLETE'
        ORDER BY aer.createDate DESC LIMIT 30
    </select>

    <select id="selectDbReportRecord"
            resultType="com.digiwin.escloud.aioitms.report.model.db.DbReportRecord">
        SELECT * FROM aiops_db_report_record
        WHERE eid = #{eid}
          AND reportType = '3'
          AND createTime >= DATE_SUB(NOW(), INTERVAL 3 MONTH)
        ORDER BY createTime DESC LIMIT 30
    </select>

    <!-- 查询code=100的设备名称 -->
    <select id="queryDeviceNamesByCode100" resultType="java.util.Map">
        SELECT deviceName,'100' as aiopsItem
        FROM (
            SELECT
                ad.deviceName,
                MAX(adcd.__version__) AS latest_version
            FROM aiops_device_collect_detail adcd
             JOIN aiops_device_collect adc ON adcd.adcId = adc.id
             JOIN aiops_device ad ON ad.deviceId = adc.deviceId
            JOIN aiops_instance ai on adcd.aiId = ai.id
            WHERE
                adcd.accId IN (102000000002003)
                AND ad.eid = #{eid}
                AND adcd.isEnable = true
              AND ai.aiopsAuthStatus IN ('AUTHED','UNAUTH')
                AND ad.deviceName IS NOT NULL
            GROUP BY ad.deviceName
        ) AS grouped_devices
        ORDER BY latest_version DESC
        LIMIT 10
    </select>

    <!-- 查询code=08的设备名称 -->
    <select id="queryDeviceNamesByCode08" resultType="java.util.Map">
        SELECT deviceName,'08' as aiopsItem
        FROM (
            SELECT
                ad.deviceName,
                MAX(adcd.__version__) AS latest_version
            FROM aiops_device_collect_detail adcd
             JOIN aiops_device_collect adc ON adcd.adcId = adc.id
             JOIN aiops_device ad ON ad.deviceId = adc.deviceId
            JOIN aiops_instance ai on adcd.aiId = ai.id
            WHERE
                adcd.accId IN (550562617242176)
                AND ad.eid = #{eid}
                AND adcd.isEnable = true
              AND ai.aiopsAuthStatus IN ('AUTHED','UNAUTH')
                AND ad.deviceName IS NOT NULL
            GROUP BY ad.deviceName
        ) AS grouped_devices
        ORDER BY latest_version DESC
        LIMIT 10
    </select>

</mapper>
